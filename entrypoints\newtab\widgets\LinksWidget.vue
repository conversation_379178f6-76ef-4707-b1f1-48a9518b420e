<template>
  <div 
    class="links-widget"
    :class="[
      `position-${position}`,
      `align-${alignment}`,
      `display-${displayMode}`
    ]"
  >
    <!-- Ссылки без папок -->
    <div class="links-container">
      <draggable
        v-model="rootLinks"
        :disabled="!enableDragDrop"
        item-key="id"
        class="links-grid"
        :class="{ [`items-per-row-${itemsPerRow}`]: displayMode === 'grid' }"
        @end="onDragEnd"
      >
        <template #item="{ element: link }">
          <LinkItem
            :link="link"
            :display-settings="displaySettings"
            :styles="styles"
            @click="openLink(link)"
            @edit="editLink(link)"
            @delete="deleteLink(link)"
          />
        </template>
      </draggable>
    </div>

    <!-- Папки -->
    <div v-if="folders.length > 0" class="folders-container">
      <div
        v-for="folder in folders"
        :key="folder.id"
        class="folder-item"
        :class="{ 'expanded': folder.expanded }"
      >
        <!-- Заголовок папки -->
        <div 
          class="folder-header"
          @click="toggleFolder(folder.id)"
        >
          <div class="folder-icon">
            <IconComponent :icon="folder.icon" :size="24" />
          </div>
          <span class="folder-title">{{ folder.title }}</span>
          <div class="folder-toggle">
            <ChevronDown 
              :class="{ 'rotated': folder.expanded }"
              :size="16"
            />
          </div>
        </div>

        <!-- Содержимое папки -->
        <transition name="folder-content">
          <div v-if="folder.expanded" class="folder-content">
            <draggable
              v-model="folderLinks[folder.id]"
              :disabled="!enableDragDrop"
              item-key="id"
              class="folder-links"
              @end="onDragEnd"
            >
              <template #item="{ element: link }">
                <LinkItem
                  :link="link"
                  :display-settings="displaySettings"
                  :styles="styles"
                  @click="openLink(link)"
                  @edit="editLink(link)"
                  @delete="deleteLink(link)"
                />
              </template>
            </draggable>
          </div>
        </transition>
      </div>
    </div>

    <!-- Кнопка добавления ссылки -->
    <button
      v-if="showAddButton"
      class="add-link-btn"
      @click="showAddDialog = true"
      :title="'Добавить ссылку'"
    >
      <Plus :size="20" />
    </button>

    <!-- Диалог добавления ссылки -->
    <AddLinkDialog
      v-if="showAddDialog"
      @close="showAddDialog = false"
      @add="addNewLink"
    />
  </div>
</template>

<script setup>
import { computed, ref, onMounted, onUnmounted } from 'vue';
import { ChevronDown, Plus } from 'lucide-vue-next';
import draggable from 'vuedraggable';
import { pluginSystem } from '../utils/pluginSystem.js';
import LinkItem from '../components/LinkItem.vue';
import IconComponent from '../components/IconComponent.vue';
import AddLinkDialog from '../components/AddLinkDialog.vue';

const props = defineProps({
  pluginId: {
    type: String,
    required: true
  }
});

// Получаем плагин
const plugin = computed(() => pluginSystem.getPlugin(props.pluginId));

// Реактивные данные
const rootLinks = ref([]);
const folders = ref([]);
const folderLinks = ref({});
const showAddDialog = ref(false);
const showAddButton = ref(true);

// Настройки отображения
const displaySettings = computed(() => plugin.value?.getDisplaySettings() || {});
const styles = computed(() => plugin.value?.getStyles() || {});

const displayMode = computed(() => displaySettings.value.displayMode || 'grid');
const itemsPerRow = computed(() => displaySettings.value.itemsPerRow || 6);
const enableDragDrop = computed(() => plugin.value?.getSetting('enableDragDrop') || true);
const position = computed(() => plugin.value?.getSetting('position') || 'bottom');
const alignment = computed(() => plugin.value?.getSetting('alignment') || 'center');

// Методы
const updateData = () => {
  if (!plugin.value) return;
  
  rootLinks.value = plugin.value.getLinksWithoutFolder();
  folders.value = plugin.value.getFolders();
  
  // Обновляем ссылки в папках
  const newFolderLinks = {};
  folders.value.forEach(folder => {
    newFolderLinks[folder.id] = plugin.value.getLinksInFolder(folder.id);
  });
  folderLinks.value = newFolderLinks;
};

const openLink = (link) => {
  window.open(link.url, '_blank');
};

const editLink = (link) => {
  // TODO: Открыть диалог редактирования
  console.log('Edit link:', link);
};

const deleteLink = async (link) => {
  if (confirm(`Удалить ссылку "${link.title}"?`)) {
    await plugin.value.removeLink(link.id);
  }
};

const toggleFolder = async (folderId) => {
  await plugin.value.toggleFolder(folderId);
};

const addNewLink = async (linkData) => {
  await plugin.value.addLink(linkData);
  showAddDialog.value = false;
};

const onDragEnd = async (event) => {
  // Обновляем порядок ссылок после перетаскивания
  if (event.to === event.from) {
    const linkIds = Array.from(event.to.children).map(el => el.dataset.linkId);
    await plugin.value.reorderLinks(linkIds);
  }
};

// Слушатели событий плагина
const onLinkAdded = () => updateData();
const onLinkUpdated = () => updateData();
const onLinkRemoved = () => updateData();
const onFolderToggled = () => updateData();
const onLinksReordered = () => updateData();

onMounted(() => {
  if (plugin.value) {
    updateData();
    
    // Подписываемся на события плагина
    plugin.value.addEventListener('link-added', onLinkAdded);
    plugin.value.addEventListener('link-updated', onLinkUpdated);
    plugin.value.addEventListener('link-removed', onLinkRemoved);
    plugin.value.addEventListener('folder-toggled', onFolderToggled);
    plugin.value.addEventListener('links-reordered', onLinksReordered);
  }
});

onUnmounted(() => {
  if (plugin.value) {
    // Отписываемся от событий
    plugin.value.removeEventListener('link-added', onLinkAdded);
    plugin.value.removeEventListener('link-updated', onLinkUpdated);
    plugin.value.removeEventListener('link-removed', onLinkRemoved);
    plugin.value.removeEventListener('folder-toggled', onFolderToggled);
    plugin.value.removeEventListener('links-reordered', onLinksReordered);
  }
});
</script>

<style scoped>
.links-widget {
  position: relative;
  z-index: 10;
  max-width: 100%;
}

/* Позиционирование */
.position-top {
  position: absolute;
  top: 20px;
  left: 50%;
  transform: translateX(-50%);
}

.position-center {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
}

.position-bottom {
  position: absolute;
  bottom: 20px;
  left: 50%;
  transform: translateX(-50%);
}

/* Выравнивание */
.align-left {
  align-items: flex-start;
}

.align-center {
  align-items: center;
}

.align-right {
  align-items: flex-end;
}

/* Контейнеры */
.links-container {
  margin-bottom: 20px;
}

.links-grid {
  display: grid;
  gap: 12px;
  justify-content: center;
}

.display-grid .links-grid {
  grid-template-columns: repeat(var(--items-per-row, 6), 1fr);
}

.display-list .links-grid {
  grid-template-columns: 1fr;
  max-width: 400px;
}

.display-compact .links-grid {
  grid-template-columns: repeat(auto-fit, minmax(60px, 1fr));
}

/* Папки */
.folders-container {
  margin-top: 20px;
}

.folder-item {
  margin-bottom: 16px;
  background: rgba(255, 255, 255, 0.05);
  border-radius: 12px;
  overflow: hidden;
  backdrop-filter: blur(10px);
}

.folder-header {
  display: flex;
  align-items: center;
  padding: 12px 16px;
  cursor: pointer;
  transition: background-color 0.2s ease;
}

.folder-header:hover {
  background: rgba(255, 255, 255, 0.1);
}

.folder-icon {
  margin-right: 12px;
  color: rgba(255, 255, 255, 0.8);
}

.folder-title {
  flex: 1;
  font-weight: 500;
  color: white;
}

.folder-toggle {
  color: rgba(255, 255, 255, 0.6);
  transition: transform 0.2s ease;
}

.folder-toggle .rotated {
  transform: rotate(180deg);
}

.folder-content {
  padding: 16px;
  border-top: 1px solid rgba(255, 255, 255, 0.1);
}

.folder-links {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(80px, 1fr));
  gap: 12px;
}

/* Анимации папок */
.folder-content-enter-active,
.folder-content-leave-active {
  transition: all 0.3s ease;
  overflow: hidden;
}

.folder-content-enter-from,
.folder-content-leave-to {
  max-height: 0;
  padding-top: 0;
  padding-bottom: 0;
  opacity: 0;
}

.folder-content-enter-to,
.folder-content-leave-from {
  max-height: 500px;
  opacity: 1;
}

/* Кнопка добавления */
.add-link-btn {
  position: fixed;
  bottom: 20px;
  right: 80px;
  width: 50px;
  height: 50px;
  border-radius: 50%;
  background: rgba(139, 69, 199, 0.8);
  border: none;
  color: white;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.3s ease;
  backdrop-filter: blur(10px);
  z-index: 100;
}

.add-link-btn:hover {
  background: rgba(139, 69, 199, 1);
  transform: scale(1.1);
}

/* Адаптивность */
@media (max-width: 768px) {
  .display-grid .links-grid {
    grid-template-columns: repeat(4, 1fr);
  }
  
  .position-top,
  .position-bottom {
    position: relative;
    transform: none;
    left: auto;
  }
  
  .add-link-btn {
    bottom: 80px;
    right: 20px;
  }
}

@media (max-width: 480px) {
  .display-grid .links-grid {
    grid-template-columns: repeat(3, 1fr);
  }
}
</style>
