<template>
	<div class="links-widget widget">
		<!-- Список ссылок -->
		<div class="links-grid">
			<div
				v-for="(link, index) in links"
				:key="index"
				class="link-card"
				@click="openLink(link)"
			>
				<div class="link-content">
					<div class="link-label">{{ link.label || $t("links.noLabel") }}</div>
					<div class="link-url">{{ link.url }}</div>
					<div class="link-stats">
						<span class="visit-count"
							>{{ link.visits || 0 }} {{ $t("links.visitCount") }}</span
						>
						<button
							@click.stop="toggleFavorite(index)"
							class="favorite-btn"
							:class="{ 'is-favorite': link.favorite }"
							:title="
								link.favorite ? $t('links.unfavorite') : $t('links.favorite')
							"
						>
							<Heart class="favorite-icon" :class="{ filled: link.favorite }" />
						</button>
					</div>
				</div>
			</div>
		</div>

		<!-- Кнопки управления -->
		<div class="links-actions">
			<button @click="addDefaultLinks" class="btn btn-secondary btn-sm">
				{{ $t("links.addDefaultLinks") }}
			</button>
			<button @click="exportLinks" class="btn btn-primary btn-sm">
				{{ $t("links.exportLinks") }}
			</button>
		</div>
	</div>
</template>

<script setup>
import { computed } from "vue";
import { useSettingsStore } from "../stores/settingsStore";
import { Heart } from "lucide-vue-next";

const settingsStore = useSettingsStore();

// Вычисляемые свойства
const links = computed(() => settingsStore.settings.links);

// Методы
const handleImageError = (event) => {
	event.target.style.display = "none";
};

const toggleFavorite = (index) => {
	const updatedLinks = [...links.value];
	updatedLinks[index].favorite = !updatedLinks[index].favorite;
	updatedLinks[index].visits = (updatedLinks[index].visits || 0) + 1;
	settingsStore.updateLinks(updatedLinks);
};

const openLink = (link) => {
	// Увеличиваем счетчик посещений
	const updatedLinks = links.value.map((l) =>
		l.url === link.url ? { ...l, visits: (l.visits || 0) + 1 } : l
	);
	settingsStore.updateLinks(updatedLinks);

	// Открываем ссылку
	window.open(link.url, "_blank", "noopener,noreferrer");
};

const addDefaultLinks = () => {
	const defaultLinks = [
		{
			label: "GitHub",
			url: "https://github.com",
			category: "Разработка",
			favorite: true,
		},
		{
			label: "Stack Overflow",
			url: "https://stackoverflow.com",
			category: "Разработка",
		},
		{
			label: "MDN Web Docs",
			url: "https://developer.mozilla.org",
			category: "Документация",
		},
		{ label: "CSS Tricks", url: "https://css-tricks.com", category: "CSS" },
		{ label: "Dev.to", url: "https://dev.to", category: "Блоги" },
	];
	settingsStore.updateLinks(defaultLinks);
};

const exportLinks = () => {
	const dataStr = JSON.stringify(links.value, null, 2);
	const dataBlob = new Blob([dataStr], { type: "application/json" });
	const url = URL.createObjectURL(dataBlob);
	const link = document.createElement("a");
	link.href = url;
	link.download = "veyra-links.json";
	link.click();
	URL.revokeObjectURL(url);
};
</script>

<style scoped>
.links-widget {
	padding: var(--spacing-lg);
	background: rgba(255, 255, 255, 0.1);
	border: 1px solid rgba(255, 255, 255, 0.2);
	border-radius: var(--border-radius);
	backdrop-filter: blur(10px);
}

.links-grid {
	display: grid;
	grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
	gap: var(--spacing-md);
	margin-bottom: var(--spacing-lg);
}

.link-card {
	background: rgba(255, 255, 255, 0.05);
	border: 1px solid rgba(255, 255, 255, 0.1);
	border-radius: var(--border-radius);
	padding: var(--spacing-md);
	cursor: pointer;
	transition: all 0.3s ease;
}

.link-card:hover {
	background: rgba(255, 255, 255, 0.1);
	transform: translateY(-2px);
	box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2);
}

.link-content {
	display: flex;
	flex-direction: column;
	gap: var(--spacing-sm);
}

.link-label {
	font-size: 1.1rem;
	font-weight: 600;
	color: var(--text-primary);
}

.link-url {
	font-size: 0.9rem;
	color: var(--text-secondary);
	word-break: break-all;
}

.link-stats {
	display: flex;
	justify-content: space-between;
	align-items: center;
	margin-top: var(--spacing-sm);
}

.visit-count {
	font-size: 0.8rem;
	color: var(--text-muted);
}

.favorite-btn {
	background: none;
	border: none;
	cursor: pointer;
	padding: var(--spacing-xs);
	border-radius: 50%;
	transition: all 0.2s ease;
}

.favorite-btn:hover {
	background: rgba(255, 255, 255, 0.1);
}

.favorite-icon {
	width: 18px;
	height: 18px;
	color: var(--text-muted);
	transition: all 0.2s ease;
}

.favorite-icon.filled {
	color: #ef4444;
	fill: #ef4444;
}

.favorite-btn:hover .favorite-icon {
	color: #ef4444;
}

.links-actions {
	display: flex;
	gap: var(--spacing-md);
	justify-content: center;
}

.btn {
	padding: var(--spacing-sm) var(--spacing-md);
	border: none;
	border-radius: var(--border-radius);
	cursor: pointer;
	font-size: 0.9em;
	font-weight: 500;
	transition: all 0.2s ease;
}

.btn-primary {
	background: var(--accent-color);
	color: white;
}

.btn-primary:hover {
	background: var(--accent-color-hover);
}

.btn-secondary {
	background: rgba(255, 255, 255, 0.1);
	color: var(--text-primary);
	border: 1px solid rgba(255, 255, 255, 0.2);
}

.btn-secondary:hover {
	background: rgba(255, 255, 255, 0.2);
}

.btn-sm {
	padding: var(--spacing-xs) var(--spacing-sm);
	font-size: 0.8em;
}

/* Адаптивность */
@media (max-width: 768px) {
	.links-grid {
		grid-template-columns: 1fr;
	}

	.links-actions {
		flex-direction: column;
		align-items: center;
	}
}

@media (max-width: 480px) {
	.link-card {
		padding: var(--spacing-sm);
	}

	.link-label {
		font-size: 1rem;
	}

	.link-url {
		font-size: 0.8rem;
	}
}
</style>
