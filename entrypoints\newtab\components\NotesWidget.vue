<template>
	<div class="notes-widget widget">
		<!-- Список заметок -->
		<div class="notes-list">
			<div
				v-for="(note, index) in notes"
				:key="index"
				class="note-item"
				:class="{ editing: editingIndex === index }"
			>
				<div v-if="editingIndex === index" class="note-edit">
					<textarea
						v-model="editingText"
						class="note-textarea"
						:placeholder="$t('notes.notePlaceholder')"
						@keydown.ctrl.enter="saveNote(index)"
						@keydown.escape="cancelEdit"
						ref="textareaRef"
					></textarea>
					<div class="note-actions">
						<button @click="saveNote(index)" class="btn btn-primary btn-sm">
							{{ $t("notes.saveNote") }}
						</button>
						<button @click="cancelEdit" class="btn btn-secondary btn-sm">
							{{ $t("notes.cancelEdit") }}
						</button>
					</div>
				</div>

				<div v-else class="note-display">
					<div class="note-text">{{ note }}</div>
					<div class="note-actions">
						<button @click="editNote(index)" class="btn btn-ghost btn-sm">
							{{ $t("notes.editNote") }}
						</button>
						<button @click="removeNote(index)" class="btn btn-danger btn-sm">
							{{ $t("notes.deleteNote") }}
						</button>
					</div>
				</div>
			</div>
		</div>

		<!-- Кнопки управления -->
		<div class="notes-actions">
			<button @click="addNote" class="btn btn-primary">
				{{ $t("notes.addNote") }}
			</button>
			<button @click="clearAllNotes" class="btn btn-secondary">
				{{ $t("notes.clearAll") }}
			</button>
		</div>
	</div>
</template>

<script setup>
import { ref, onMounted, watch, nextTick } from "vue";

// Реактивные данные
const notes = ref([]);
const editingIndex = ref(-1);
const editingText = ref("");
const textareaRef = ref(null);

// Константы
const STORAGE_KEY = "veyra-notes";

// Методы
const loadNotes = () => {
	try {
		const savedNotes = localStorage.getItem(STORAGE_KEY);
		if (savedNotes) {
			notes.value = JSON.parse(savedNotes);
		} else {
			// Создаем заметку по умолчанию
			notes.value = ["Добро пожаловать в Veyra! Это ваша первая заметка."];
			saveNotes();
		}
	} catch (error) {
		console.error("Ошибка при загрузке заметок:", error);
		notes.value = ["Ошибка загрузки заметок"];
	}
};

const saveNotes = () => {
	try {
		localStorage.setItem(STORAGE_KEY, JSON.stringify(notes.value));
	} catch (error) {
		console.error("Ошибка при сохранении заметок:", error);
	}
};

const addNote = () => {
	notes.value.push("");
	editingIndex.value = notes.value.length - 1;
	editingText.value = "";
	saveNotes();

	// Фокусируемся на новом текстовом поле
	nextTick(() => {
		if (textareaRef.value) {
			textareaRef.value.focus();
		}
	});
};

const editNote = (index) => {
	editingIndex.value = index;
	editingText.value = notes.value[index];

	// Фокусируемся на текстовом поле
	nextTick(() => {
		if (textareaRef.value) {
			textareaRef.value.focus();
		}
	});
};

const saveNote = (index) => {
	if (editingText.value.trim()) {
		notes.value[index] = editingText.value.trim();
		saveNotes();
	}
	editingIndex.value = -1;
	editingText.value = "";
};

const cancelEdit = () => {
	editingIndex.value = -1;
	editingText.value = "";
};

const removeNote = (index) => {
	if (notes.value.length > 1) {
		notes.value.splice(index, 1);
		saveNotes();
	}
};

const clearAllNotes = () => {
	if (confirm("Вы уверены, что хотите удалить все заметки?")) {
		notes.value = ["Добро пожаловать в Veyra! Это ваша первая заметка."];
		saveNotes();
	}
};

// Наблюдатели
watch(notes, saveNotes, { deep: true });

// Жизненный цикл
onMounted(() => {
	loadNotes();
});
</script>

<style scoped>
.notes-widget {
	padding: var(--spacing-lg);
	background: rgba(255, 255, 255, 0.1);
	border: 1px solid rgba(255, 255, 255, 0.2);
	border-radius: var(--border-radius);
	backdrop-filter: blur(10px);
	min-height: 200px;
}

.notes-list {
	margin-bottom: var(--spacing-lg);
}

.note-item {
	margin-bottom: var(--spacing-md);
	padding: var(--spacing-md);
	background: rgba(255, 255, 255, 0.05);
	border: 1px solid rgba(255, 255, 255, 0.1);
	border-radius: var(--border-radius);
	transition: all 0.3s ease;
}

.note-item:hover {
	background: rgba(255, 255, 255, 0.08);
	transform: translateY(-1px);
}

.note-item.editing {
	background: rgba(255, 255, 255, 0.1);
	border-color: var(--accent-color);
}

.note-text {
	color: var(--text-primary);
	font-size: 1rem;
	line-height: 1.5;
	margin-bottom: var(--spacing-sm);
	white-space: pre-wrap;
	word-break: break-word;
}

.note-textarea {
	width: 100%;
	min-height: 100px;
	padding: var(--spacing-sm);
	border: 1px solid rgba(255, 255, 255, 0.2);
	border-radius: var(--border-radius);
	background: rgba(255, 255, 255, 0.1);
	color: var(--text-primary);
	font-size: 1rem;
	line-height: 1.5;
	resize: vertical;
	margin-bottom: var(--spacing-sm);
}

.note-textarea:focus {
	outline: none;
	border-color: var(--accent-color);
	background: rgba(255, 255, 255, 0.15);
}

.note-actions {
	display: flex;
	gap: var(--spacing-sm);
	justify-content: flex-end;
}

.btn {
	padding: var(--spacing-sm) var(--spacing-md);
	border: none;
	border-radius: var(--border-radius);
	cursor: pointer;
	font-size: 0.9em;
	font-weight: 500;
	transition: all 0.2s ease;
}

.btn-primary {
	background: var(--accent-color);
	color: white;
}

.btn-primary:hover {
	background: var(--accent-color-hover);
	transform: translateY(-1px);
}

.btn-secondary {
	background: rgba(255, 255, 255, 0.1);
	color: var(--text-primary);
	border: 1px solid rgba(255, 255, 255, 0.2);
}

.btn-secondary:hover {
	background: rgba(255, 255, 255, 0.2);
}

.btn-danger {
	background: var(--danger-color);
	color: white;
}

.btn-danger:hover {
	background: var(--danger-color-hover);
}

.btn-ghost {
	background: transparent;
	color: var(--text-primary);
	border: 1px solid rgba(255, 255, 255, 0.2);
}

.btn-ghost:hover {
	background: rgba(255, 255, 255, 0.1);
}

.btn-sm {
	padding: var(--spacing-xs) var(--spacing-sm);
	font-size: 0.8em;
}

.notes-actions {
	display: flex;
	gap: var(--spacing-md);
	justify-content: center;
}

/* Адаптивность */
@media (max-width: 768px) {
	.note-actions {
		flex-direction: column;
		align-items: stretch;
	}

	.notes-actions {
		flex-direction: column;
		align-items: center;
	}
}

@media (max-width: 480px) {
	.note-item {
		padding: var(--spacing-sm);
	}

	.note-text {
		font-size: 0.9rem;
	}

	.note-textarea {
		min-height: 80px;
		font-size: 0.9rem;
	}
}
</style>
