<script setup>
import { computed, ref, onMounted, onUnmounted } from "vue";
import { useSettingsStore } from "../stores/settingsStore";

const settingsStore = useSettingsStore();

// Реактивные данные
const showParticles = ref(false);

// Вычисляемые свойства
const backgroundType = computed(() => settingsStore.settings.backgroundType);
const solidColor = computed(() => settingsStore.settings.solidColor);
const gradient = computed(() => settingsStore.settings.gradient);
const backgroundImage = computed(() => settingsStore.settings.backgroundImage);
const backgroundBlur = computed(() => settingsStore.settings.backgroundBlur);
const backgroundOpacity = computed(
	() => settingsStore.settings.backgroundOpacity
);

const backgroundStyle = computed(() => {
	if (gradient.value.type === "linear") {
		return {
			background: `linear-gradient(${
				gradient.value.angle
			}deg, ${gradient.value.colors.join(", ")})`,
			animation: gradient.value.animation
				? `gradientMove ${gradient.value.animationSpeed}s ${gradient.value.animationTiming} infinite`
				: "none",
		};
	} else if (gradient.value.type === "radial") {
		return {
			background: `radial-gradient(circle at ${gradient.value.posX}% ${
				gradient.value.posY
			}%, ${gradient.value.colors.join(", ")})`,
			animation: gradient.value.animation
				? `gradientPulse ${gradient.value.animationSpeed}s ${gradient.value.animationTiming} infinite`
				: "none",
		};
	}
	return {};
});

const imageBackgroundStyle = computed(() => ({
	backgroundImage: `url(${backgroundImage.value})`,
	filter: `blur(${backgroundBlur.value}px)`,
	opacity: backgroundOpacity.value,
}));

// Методы
const getParticleStyle = (index) => {
	const delay = (index * 0.1) % 3;
	const size = Math.random() * 4 + 2;
	const left = Math.random() * 100;
	const animationDuration = Math.random() * 10 + 10;

	return {
		left: `${left}%`,
		width: `${size}px`,
		height: `${size}px`,
		animationDelay: `${delay}s`,
		animationDuration: `${animationDuration}s`,
	};
};

// Жизненный цикл
onMounted(() => {
	// Показываем частицы только если включены анимации
	if (settingsStore.settings.animations) {
		showParticles.value = true;
	}
});

onUnmounted(() => {
	showParticles.value = false;
});
</script>

<template>
	<div class="background-container">
		<!-- Градиентный фон -->
		<div
			v-if="backgroundType === 'gradient'"
			class="gradient-background"
			:style="backgroundStyle"
		></div>

		<!-- Сплошной цвет -->
		<div
			v-else-if="backgroundType === 'solid'"
			class="solid-background"
			:style="{ backgroundColor: solidColor }"
		></div>

		<!-- Фоновое изображение -->
		<div
			v-else-if="backgroundType === 'image' && backgroundImage"
			class="image-background"
			:style="imageBackgroundStyle"
		>
			<div class="image-overlay"></div>
		</div>

		<!-- Анимированные частицы -->
		<div v-if="showParticles" class="particles">
			<div
				v-for="i in 20"
				:key="i"
				class="particle"
				:style="getParticleStyle(i)"
			></div>
		</div>
	</div>
</template>

<style scoped>
.background-container {
	position: fixed;
	top: 0;
	left: 0;
	width: 100vw;
	height: 100vh;
	z-index: -1;
	overflow: hidden;
}

.gradient-background,
.solid-background,
.image-background {
	position: absolute;
	top: 0;
	left: 0;
	width: 100%;
	height: 100%;
	transition: all var(--transition-normal);
}

.gradient-background {
	background-size: 200% 200%;
	will-change: background-position;
	transform: translateZ(0);
	backface-visibility: hidden;
}

.solid-background {
	background-color: var(--bg-primary);
}

.image-background {
	background-size: cover;
	background-position: center;
	background-repeat: no-repeat;
}

.image-overlay {
	position: absolute;
	top: 0;
	left: 0;
	width: 100%;
	height: 100%;
	background: rgba(0, 0, 0, 0.3);
}

/* Анимации градиента */
@keyframes gradientMove {
	0% {
		background-position: 0% 50%;
	}
	50% {
		background-position: 100% 50%;
	}
	100% {
		background-position: 0% 50%;
	}
}

@keyframes gradientPulse {
	0%,
	100% {
		transform: scale(1);
	}
	50% {
		transform: scale(1.1);
	}
}

/* Анимированные частицы */
.particles {
	position: absolute;
	top: 0;
	left: 0;
	width: 100%;
	height: 100%;
	pointer-events: none;
}

.particle {
	position: absolute;
	background: rgba(255, 255, 255, 0.1);
	border-radius: 50%;
	animation: float linear infinite;
	will-change: transform;
	transform: translateZ(0);
	backface-visibility: hidden;
}

@keyframes float {
	0% {
		transform: translateY(100vh) translateX(0);
		opacity: 0;
	}
	10% {
		opacity: 1;
	}
	90% {
		opacity: 1;
	}
	100% {
		transform: translateY(-100px) translateX(100px);
		opacity: 0;
	}
}

/* Оптимизация производительности */
@media (prefers-reduced-motion: reduce) {
	.gradient-background {
		animation: none !important;
	}

	.particle {
		animation: none !important;
	}

	.gradient-background,
	.solid-background,
	.image-background {
		transition: none !important;
	}
}

/* Адаптивность */
@media (max-width: 768px) {
	.particle {
		display: none; /* Скрываем частицы на мобильных для производительности */
	}
}

/* Темная тема */
[data-theme="dark"] .particle {
	background: rgba(255, 255, 255, 0.05);
}

/* Светлая тема */
[data-theme="light"] .particle {
	background: rgba(0, 0, 0, 0.05);
}
</style>
