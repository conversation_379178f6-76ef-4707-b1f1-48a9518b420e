<template>
	<div class="settings-panel-overlay" @click="closePanel">
		<div class="settings-panel widget" @click.stop>
			<div class="settings-header">
				<h3 class="settings-title">
					<Settings class="settings-icon" />
					{{ $t("settings.title") }}
				</h3>
				<div class="settings-actions">
					<button
						@click="resetToDefaults"
						class="btn btn-ghost btn-sm"
						:title="$t('settings.resetToDefaults')"
					>
						<RotateCcw class="action-icon" />
						{{ $t("settings.resetToDefaults") }}
					</button>

					<button
						@click="exportSettings"
						class="btn btn-ghost btn-sm"
						:title="$t('settings.export')"
					>
						<Download class="action-icon" />
						{{ $t("settings.export") }}
					</button>

					<button
						@click="importSettings"
						class="btn btn-ghost btn-sm"
						:title="$t('settings.import')"
					>
						<Upload class="action-icon" />
						{{ $t("settings.import") }}
					</button>

					<button
						@click="closePanel"
						class="btn btn-ghost btn-sm close-btn"
						:title="$t('common.close')"
					>
						<X class="action-icon" />
					</button>
				</div>
			</div>

			<div class="settings-content">
				<!-- Основные настройки -->
				<div class="setting-group">
					<h4 class="group-title">
						<Layout class="group-icon" />
						{{ $t("settings.general") }}
					</h4>

					<div class="setting-item">
						<label>
							<input
								type="checkbox"
								v-model="settings.showTime"
								@change="updateSetting('showTime', $event.target.checked)"
							/>
							<span>{{ $t("settings.showTime") }}</span>
						</label>
					</div>

					<div class="setting-item">
						<label>
							<input
								type="checkbox"
								v-model="settings.showLinks"
								@change="updateSetting('showLinks', $event.target.checked)"
							/>
							<span>{{ $t("settings.showLinks") }}</span>
						</label>
					</div>

					<div class="setting-item">
						<label>
							<span>{{ $t("settings.language") }}</span>
							<select
								v-model="settings.language"
								@change="updateSetting('language', $event.target.value)"
							>
								<option value="ru">Русский</option>
								<option value="en">English</option>
							</select>
						</label>
					</div>
				</div>

				<!-- Настройки фона -->
				<div class="setting-group">
					<h4 class="group-title">
						<Brush class="group-icon" />
						{{ $t("settings.background") }}
					</h4>

					<div class="setting-item">
						<label>
							<span>{{ $t("settings.backgroundType") }}</span>
							<select
								v-model="settings.backgroundType"
								@change="updateSetting('backgroundType', $event.target.value)"
							>
								<option value="gradient">{{ $t("settings.gradient") }}</option>
								<option value="solid">{{ $t("settings.solid") }}</option>
								<option value="image">{{ $t("settings.image") }}</option>
							</select>
						</label>
					</div>

					<div v-if="settings.backgroundType === 'solid'" class="setting-item">
						<label>
							<span>{{ $t("settings.solidColor") }}</span>
							<input
								type="color"
								v-model="settings.solidColor"
								@change="updateSetting('solidColor', $event.target.value)"
							/>
						</label>
					</div>

					<div
						v-if="settings.backgroundType === 'gradient'"
						class="gradient-settings"
					>
						<div class="setting-item">
							<label>
								<span>{{ $t("settings.gradientType") }}</span>
								<select
									v-model="settings.gradient.type"
									@change="updateGradientSetting('type', $event.target.value)"
								>
									<option value="linear">{{ $t("settings.linear") }}</option>
									<option value="radial">{{ $t("settings.radial") }}</option>
								</select>
							</label>
						</div>

						<div class="setting-item">
							<label>
								<span>{{ $t("settings.gradientAngle") }}</span>
								<input
									type="range"
									min="0"
									max="360"
									v-model="settings.gradient.angle"
									@input="
										updateGradientSetting(
											'angle',
											parseInt($event.target.value)
										)
									"
								/>
								<span class="range-value">{{ settings.gradient.angle }}°</span>
							</label>
						</div>

						<div class="setting-item">
							<label>
								<span>{{ $t("settings.gradientColors") }}</span>
								<div class="color-inputs">
									<input
										v-for="(color, index) in settings.gradient.colors"
										:key="index"
										type="color"
										v-model="settings.gradient.colors[index]"
										@change="
											updateGradientSetting('colors', settings.gradient.colors)
										"
									/>
									<button
										@click="addGradientColor"
										class="btn btn-ghost btn-sm"
										:title="$t('settings.addColor')"
									>
										<Plus class="action-icon" />
									</button>
									<button
										v-if="settings.gradient.colors.length > 2"
										@click="removeGradientColor"
										class="btn btn-ghost btn-sm"
										:title="$t('settings.removeColor')"
									>
										<X class="action-icon" />
									</button>
								</div>
							</label>
						</div>
					</div>
				</div>

				<!-- Настройки темы -->
				<div class="setting-group">
					<h4 class="group-title">
						<Sun class="group-icon" />
						{{ $t("settings.theme") }}
					</h4>

					<div class="setting-item">
						<label>
							<span>{{ $t("settings.theme") }}</span>
							<select
								v-model="settings.theme"
								@change="updateSetting('theme', $event.target.value)"
							>
								<option value="dark">{{ $t("settings.dark") }}</option>
								<option value="light">{{ $t("settings.light") }}</option>
								<option value="auto">{{ $t("settings.auto") }}</option>
							</select>
						</label>
					</div>

					<div class="setting-item">
						<label>
							<span>{{ $t("settings.accentColor") }}</span>
							<input
								type="color"
								v-model="settings.accentColor"
								@change="updateSetting('accentColor', $event.target.value)"
							/>
						</label>
					</div>

					<div class="setting-item">
						<label>
							<span>{{ $t("settings.borderRadius") }}</span>
							<select
								v-model="settings.borderRadius"
								@change="updateSetting('borderRadius', $event.target.value)"
							>
								<option value="small">{{ $t("settings.small") }}</option>
								<option value="medium">{{ $t("settings.medium") }}</option>
								<option value="large">{{ $t("settings.large") }}</option>
							</select>
						</label>
					</div>

					<div class="setting-item">
						<label>
							<input
								type="checkbox"
								v-model="settings.shadows"
								@change="updateSetting('shadows', $event.target.checked)"
							/>
							<span>{{ $t("settings.shadows") }}</span>
						</label>
					</div>

					<div class="setting-item">
						<label>
							<input
								type="checkbox"
								v-model="settings.animations"
								@change="updateSetting('animations', $event.target.checked)"
							/>
							<span>{{ $t("settings.animations") }}</span>
						</label>
					</div>
				</div>

				<!-- Настройки ссылок -->
				<div class="setting-group">
					<h4 class="group-title">
						<Link class="group-icon" />
						{{ $t("settings.links") }}
					</h4>

					<div
						v-for="(link, index) in settings.links"
						:key="index"
						class="link-item"
					>
						<div class="link-inputs">
							<input
								type="text"
								v-model="link.label"
								:placeholder="$t('settings.linkLabel')"
								@blur="updateLinks"
							/>
							<input
								type="url"
								v-model="link.url"
								:placeholder="$t('settings.linkUrl')"
								@blur="updateLinks"
							/>
							<button
								@click="removeLink(index)"
								class="btn btn-danger btn-sm"
								:title="$t('settings.removeLink')"
							>
								<Trash2 class="action-icon" />
							</button>
						</div>
					</div>

					<button @click="addLink" class="btn btn-primary btn-sm">
						<Plus class="action-icon" />
						{{ $t("settings.addLink") }}
					</button>
				</div>

				<!-- Настройки погоды -->
				<div class="setting-group">
					<h4 class="group-title">
						<Cloud class="group-icon" />
						{{ $t("settings.weather") }}
					</h4>

					<div class="setting-item">
						<label>
							<span>{{ $t("settings.openWeatherApiKey") }}</span>
							<input
								type="password"
								v-model="settings.openWeatherApiKey"
								@change="
									updateSetting('openWeatherApiKey', $event.target.value)
								"
								:placeholder="$t('settings.openWeatherApiKey')"
							/>
						</label>
					</div>

					<div class="setting-item">
						<label>
							<span>{{ $t("settings.weatherLocation") }}</span>
							<input
								type="text"
								v-model="settings.weatherLocation"
								@change="updateSetting('weatherLocation', $event.target.value)"
								:placeholder="$t('settings.weatherLocation')"
							/>
						</label>
					</div>
				</div>

				<!-- Настройки производительности -->
				<div class="setting-group">
					<h4 class="group-title">
						<Zap class="group-icon" />
						{{ $t("settings.performance") }}
					</h4>

					<div class="setting-item">
						<label>
							<input
								type="checkbox"
								v-model="settings.reduceMotion"
								@change="updateSetting('reduceMotion', $event.target.checked)"
							/>
							<span>{{ $t("settings.reduceMotion") }}</span>
						</label>
					</div>

					<div class="setting-item">
						<label>
							<input
								type="checkbox"
								v-model="settings.lowPowerMode"
								@change="updateSetting('lowPowerMode', $event.target.checked)"
							/>
							<span>{{ $t("settings.lowPowerMode") }}</span>
						</label>
					</div>
				</div>

				<!-- Настройки уведомлений -->
				<div class="setting-group">
					<h4 class="group-title">
						<Bell class="group-icon" />
						{{ $t("settings.notifications") }}
					</h4>

					<div class="setting-item">
						<label>
							<input
								type="checkbox"
								v-model="settings.notifications"
								@change="updateSetting('notifications', $event.target.checked)"
							/>
							<span>{{ $t("settings.notifications") }}</span>
						</label>
					</div>

					<div class="setting-item">
						<label>
							<span>{{ $t("settings.notificationPosition") }}</span>
							<select
								v-model="settings.notificationPosition"
								@change="
									updateSetting('notificationPosition', $event.target.value)
								"
							>
								<option value="top-right">{{ $t("settings.topRight") }}</option>
								<option value="top-left">{{ $t("settings.topLeft") }}</option>
								<option value="bottom-right">
									{{ $t("settings.bottomRight") }}
								</option>
								<option value="bottom-left">
									{{ $t("settings.bottomLeft") }}
								</option>
							</select>
						</label>
					</div>

					<div class="setting-item">
						<label>
							<span>{{ $t("settings.notificationDuration") }}</span>
							<input
								type="number"
								v-model="settings.notificationDuration"
								@change="
									updateSetting(
										'notificationDuration',
										parseInt($event.target.value)
									)
								"
								min="1000"
								max="10000"
								step="1000"
							/>
							<span class="unit">{{ $t("common.milliseconds") }}</span>
						</label>
					</div>
				</div>

				<!-- Настройки безопасности -->
				<div class="setting-group">
					<h4 class="group-title">
						<Shield class="group-icon" />
						{{ $t("settings.security") }}
					</h4>

					<div class="setting-item">
						<label>
							<input
								type="checkbox"
								v-model="settings.autoSave"
								@change="updateSetting('autoSave', $event.target.checked)"
							/>
							<span>{{ $t("settings.autoSave") }}</span>
						</label>
					</div>

					<div class="setting-item">
						<label>
							<span>{{ $t("settings.backupInterval") }}</span>
							<input
								type="number"
								v-model="settings.backupInterval"
								@change="
									updateSetting('backupInterval', parseInt($event.target.value))
								"
								min="1"
								max="168"
							/>
							<span class="unit">{{ $t("common.hours") }}</span>
						</label>
					</div>

					<div class="setting-actions">
						<button
							@click="createBackup"
							class="btn btn-primary btn-sm"
							:title="$t('settings.createBackup')"
						>
							<Download class="action-icon" />
							{{ $t("settings.createBackup") }}
						</button>

						<button
							@click="restoreFromBackup"
							class="btn btn-secondary btn-sm"
							:title="$t('settings.restoreFromBackup')"
						>
							<Upload class="action-icon" />
							{{ $t("settings.restoreFromBackup") }}
						</button>
					</div>
				</div>

				<!-- Настройки разработчика -->
				<div class="setting-group">
					<h4 class="group-title">
						<Code class="group-icon" />
						{{ $t("settings.developer") }}
					</h4>

					<div class="setting-item">
						<label>
							<input
								type="checkbox"
								v-model="settings.debugMode"
								@change="updateSetting('debugMode', $event.target.checked)"
							/>
							<span>{{ $t("settings.debugMode") }}</span>
						</label>
					</div>

					<div class="setting-item">
						<label>
							<input
								type="checkbox"
								v-model="settings.showPerformanceMetrics"
								@change="
									updateSetting('showPerformanceMetrics', $event.target.checked)
								"
							/>
							<span>{{ $t("settings.showPerformanceMetrics") }}</span>
						</label>
					</div>
				</div>
			</div>
		</div>
	</div>
</template>

<script setup>
import { ref, computed } from "vue";
import { useSettingsStore } from "../stores/settingsStore";
import {
	Settings,
	RotateCcw,
	Upload,
	Brush,
	Sun,
	Zap,
	Shield,
	Plus,
	X,
	Trash2,
	Bell,
	Cloud,
	Code,
	Download,
	Layout,
	Link,
} from "lucide-vue-next";

const settingsStore = useSettingsStore();
const fileInput = ref(null);

// Вычисляемые свойства
const settings = computed(() => settingsStore.settings);

// Методы
const updateSetting = (key, value) => {
	settingsStore.updateSetting(key, value);
};

const updateGradientSetting = (key, value) => {
	const updatedGradient = { ...settings.value.gradient, [key]: value };
	settingsStore.updateSetting("gradient", updatedGradient);
};

const addGradientColor = () => {
	const updatedColors = [...settings.value.gradient.colors, "#ffffff"];
	updateGradientSetting("colors", updatedColors);
};

const removeGradientColor = () => {
	if (settings.value.gradient.colors.length > 2) {
		const updatedColors = settings.value.gradient.colors.slice(0, -1);
		updateGradientSetting("colors", updatedColors);
	}
};

const addLink = () => {
	const updatedLinks = [...settings.value.links, { label: "", url: "" }];
	settingsStore.updateSetting("links", updatedLinks);
};

const removeLink = (index) => {
	const updatedLinks = settings.value.links.filter((_, i) => i !== index);
	settingsStore.updateSetting("links", updatedLinks);
};

const updateLinks = () => {
	// Ссылки уже обновлены через v-model
	settingsStore.updateSetting("links", settings.value.links);
};

const resetToDefaults = () => {
	if (
		confirm(
			settings.value.language === "ru"
				? "Вы уверены, что хотите сбросить все настройки к значениям по умолчанию?"
				: "Are you sure you want to reset all settings to default values?"
		)
	) {
		settingsStore.resetToDefaults();
	}
};

const exportSettings = () => {
	const dataStr = JSON.stringify(settings.value, null, 2);
	const dataBlob = new Blob([dataStr], { type: "application/json" });
	const url = URL.createObjectURL(dataBlob);
	const link = document.createElement("a");
	link.href = url;
	link.download = "veyra-settings.json";
	link.click();
	URL.revokeObjectURL(url);
};

const importSettings = () => {
	fileInput.value.click();
};

const handleFileImport = (event) => {
	const file = event.target.files[0];
	if (file) {
		const reader = new FileReader();
		reader.onload = (e) => {
			try {
				const importedSettings = JSON.parse(e.target.result);
				if (
					confirm(
						settings.value.language === "ru"
							? "Вы уверены, что хотите импортировать эти настройки? Текущие настройки будут заменены."
							: "Are you sure you want to import these settings? Current settings will be replaced."
					)
				) {
					settingsStore.restoreFromBackup(importedSettings);
				}
			} catch (error) {
				alert(
					settings.value.language === "ru"
						? "Ошибка при импорте настроек"
						: "Error importing settings"
				);
			}
		};
		reader.readAsText(file);
	}
};

const createBackup = () => {
	const result = settingsStore.createBackup();
	if (result.success) {
		alert(
			settings.value.language === "ru"
				? "Резервная копия создана"
				: "Backup created"
		);
	} else {
		alert(
			settings.value.language === "ru"
				? "Ошибка создания резервной копии"
				: "Error creating backup"
		);
	}
};

const restoreFromBackup = () => {
	const result = settingsStore.restoreFromBackup();
	if (result.success) {
		alert(
			settings.value.language === "ru"
				? "Настройки восстановлены из резервной копии"
				: "Settings restored from backup"
		);
	} else {
		alert(
			settings.value.language === "ru"
				? "Ошибка восстановления из резервной копии"
				: "Error restoring from backup"
		);
	}
};

const closePanel = () => {
	// Эмитим событие для закрытия панели
	emit("close");
};

// Эмиты
const emit = defineEmits(["close"]);
</script>

<style scoped>
.settings-panel-overlay {
	position: fixed;
	top: 0;
	left: 0;
	right: 0;
	bottom: 0;
	background: rgba(0, 0, 0, 0.5);
	z-index: 999;
	display: flex;
	align-items: center;
	justify-content: center;
	padding: 20px;
}

.settings-panel {
	max-width: 800px;
	max-height: 90vh;
	overflow-y: auto;
	background: var(--bg-secondary);
	border: 1px solid var(--border-color);
	border-radius: var(--border-radius);
	box-shadow: var(--shadow-lg);
}

.settings-header {
	display: flex;
	align-items: center;
	justify-content: space-between;
	padding: var(--spacing-lg);
	border-bottom: 1px solid var(--border-color);
	background: var(--bg-primary);
	border-radius: var(--border-radius) var(--border-radius) 0 0;
}

.settings-title {
	display: flex;
	align-items: center;
	gap: var(--spacing-sm);
	margin: 0;
	font-size: 1.5em;
	font-weight: 600;
	color: var(--text-primary);
}

.settings-icon {
	width: 24px;
	height: 24px;
	color: var(--accent-color);
}

.settings-actions {
	display: flex;
	gap: var(--spacing-sm);
	align-items: center;
}

.close-btn {
	background: var(--danger-color);
	color: white;
}

.close-btn:hover {
	background: var(--danger-color-hover);
}

.settings-content {
	padding: var(--spacing-lg);
}

.setting-group {
	margin-bottom: var(--spacing-xl);
}

.group-title {
	display: flex;
	align-items: center;
	gap: var(--spacing-sm);
	margin: 0 0 var(--spacing-md) 0;
	font-size: 1.2em;
	font-weight: 600;
	color: var(--text-primary);
	border-bottom: 2px solid var(--accent-color);
	padding-bottom: var(--spacing-sm);
}

.group-icon {
	width: 20px;
	height: 20px;
	color: var(--accent-color);
}

.setting-item {
	margin-bottom: var(--spacing-md);
}

.setting-item label {
	display: flex;
	align-items: center;
	justify-content: space-between;
	gap: var(--spacing-md);
}

.setting-item span {
	color: var(--text-primary);
	font-weight: 500;
}

.setting-item input[type="text"],
.setting-item input[type="url"],
.setting-item input[type="password"],
.setting-item input[type="number"],
.setting-item select {
	padding: var(--spacing-sm);
	border: 1px solid var(--border-color);
	border-radius: var(--border-radius);
	background: var(--bg-primary);
	color: var(--text-primary);
	min-width: 200px;
}

.setting-item input[type="color"] {
	width: 50px;
	height: 40px;
	border: none;
	border-radius: var(--border-radius);
	cursor: pointer;
}

.setting-item input[type="checkbox"] {
	width: 18px;
	height: 18px;
	accent-color: var(--accent-color);
}

.range-value {
	color: var(--text-secondary);
	font-size: 0.9em;
	min-width: 40px;
	text-align: center;
}

.unit {
	color: var(--text-secondary);
	font-size: 0.9em;
	margin-left: var(--spacing-sm);
}

.gradient-settings {
	margin-left: var(--spacing-lg);
	padding-left: var(--spacing-lg);
	border-left: 2px solid var(--border-color);
}

.color-inputs {
	display: flex;
	gap: var(--spacing-sm);
	align-items: center;
	flex-wrap: wrap;
}

.link-item {
	margin-bottom: var(--spacing-md);
}

.link-inputs {
	display: flex;
	gap: var(--spacing-sm);
	align-items: center;
	margin-bottom: var(--spacing-sm);
}

.link-inputs input {
	flex: 1;
}

.btn {
	padding: var(--spacing-sm) var(--spacing-md);
	border: none;
	border-radius: var(--border-radius);
	cursor: pointer;
	font-size: 0.9em;
	font-weight: 500;
	transition: all 0.2s ease;
	display: flex;
	align-items: center;
	gap: var(--spacing-sm);
}

.btn-primary {
	background: var(--accent-color);
	color: white;
}

.btn-primary:hover {
	background: var(--accent-color-hover);
}

.btn-secondary {
	background: var(--bg-primary);
	color: var(--text-primary);
	border: 1px solid var(--border-color);
}

.btn-secondary:hover {
	background: var(--bg-hover);
}

.btn-danger {
	background: var(--danger-color);
	color: white;
}

.btn-danger:hover {
	background: var(--danger-color-hover);
}

.btn-ghost {
	background: transparent;
	color: var(--text-primary);
	border: 1px solid var(--border-color);
}

.btn-ghost:hover {
	background: var(--bg-hover);
}

.btn-sm {
	padding: var(--spacing-xs) var(--spacing-sm);
	font-size: 0.8em;
}

.action-icon {
	width: 16px;
	height: 16px;
}

/* Стили для скроллбара */
.settings-panel::-webkit-scrollbar {
	width: 8px;
}

.settings-panel::-webkit-scrollbar-track {
	background: var(--bg-primary);
	border-radius: 4px;
}

.settings-panel::-webkit-scrollbar-thumb {
	background: var(--border-color);
	border-radius: 4px;
}

.settings-panel::-webkit-scrollbar-thumb:hover {
	background: var(--accent-color);
}
</style>
