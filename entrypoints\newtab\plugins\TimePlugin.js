import { BasePlugin } from '../utils/BasePlugin.js';
import TimeComponent from '../widgets/TimeWidget.vue';
import TimeSettings from '../components/TimeSettings.vue';

/**
 * Плагин отображения времени Veyra
 * Поддерживает полную кастомизацию отображения и альтернативные режимы
 */
class TimePluginClass extends BasePlugin {
  constructor() {
    super({
      id: 'time',
      name: 'Время',
      version: '1.0.0',
      description: 'Отображение времени с полной кастомизацией',
      component: TimeComponent,
      settingsComponent: TimeSettings,
      defaultSettings: {
        // Основные настройки отображения
        format: '24h', // '12h', '24h'
        showSeconds: false,
        showDate: true,
        showDay: true,
        
        // Альтернативный режим
        alternativeMode: false,
        alternativeText: 'Эй, {time}, пора бы за работу!',
        
        // Позиционирование
        position: 'center', // 'center', 'top', 'bottom', 'left', 'right'
        alignment: 'center', // 'left', 'center', 'right'
        
        // Стилизация времени
        timeStyle: {
          fontSize: '4rem',
          fontWeight: '300',
          fontFamily: 'Onest',
          color: '#ffffff',
          textShadow: '0 2px 10px rgba(0, 0, 0, 0.3)',
          letterSpacing: '0.05em'
        },
        
        // Стилизация даты
        dateStyle: {
          fontSize: '1.2rem',
          fontWeight: '400',
          fontFamily: 'Onest',
          color: 'rgba(255, 255, 255, 0.8)',
          textShadow: '0 1px 5px rgba(0, 0, 0, 0.3)',
          marginTop: '0.5rem'
        },
        
        // Стилизация дня недели
        dayStyle: {
          fontSize: '1rem',
          fontWeight: '500',
          fontFamily: 'Onest',
          color: 'rgba(255, 255, 255, 0.6)',
          textShadow: '0 1px 5px rgba(0, 0, 0, 0.3)',
          marginBottom: '0.5rem',
          textTransform: 'uppercase',
          letterSpacing: '0.1em'
        },
        
        // Кастомизация отдельных элементов времени
        customElements: {
          hours: {
            enabled: true,
            fontSize: '4rem',
            fontWeight: '300',
            color: '#ffffff'
          },
          minutes: {
            enabled: true,
            fontSize: '4rem',
            fontWeight: '300',
            color: '#ffffff'
          },
          seconds: {
            enabled: false,
            fontSize: '2rem',
            fontWeight: '300',
            color: 'rgba(255, 255, 255, 0.7)',
            position: 'top-right' // 'top-right', 'bottom-right', 'inline'
          },
          separator: {
            enabled: true,
            character: ':',
            fontSize: '4rem',
            color: '#ffffff',
            animation: 'blink' // 'none', 'blink', 'fade'
          }
        },
        
        // Локализация
        locale: 'ru-RU',
        timezone: 'auto', // 'auto' или конкретная временная зона
        
        // Анимации
        enableAnimations: true,
        entranceAnimation: 'fadeIn', // 'fadeIn', 'slideUp', 'scale', 'none'
        updateAnimation: 'smooth', // 'smooth', 'flip', 'slide', 'none'
        
        // Дополнительные эффекты
        glowEffect: false,
        glowColor: '#8B45C7',
        backgroundBlur: false,
        backgroundOpacity: 0.1
      }
    });
    
    this.currentTime = new Date();
    this.timeInterval = null;
    this.animationFrame = null;
  }

  async onInit() {
    // Запускаем обновление времени
    this.startTimeUpdates();
    
    // Устанавливаем временную зону если указана
    const timezone = this.getSetting('timezone');
    if (timezone !== 'auto') {
      this.setTimezone(timezone);
    }
    
    console.log('Time plugin initialized');
  }

  async onDestroy() {
    // Останавливаем обновления времени
    this.stopTimeUpdates();
    console.log('Time plugin destroyed');
  }

  /**
   * Запускает обновление времени
   */
  startTimeUpdates() {
    this.updateTime();
    
    // Обновляем каждую секунду
    this.timeInterval = setInterval(() => {
      this.updateTime();
    }, 1000);
  }

  /**
   * Останавливает обновление времени
   */
  stopTimeUpdates() {
    if (this.timeInterval) {
      clearInterval(this.timeInterval);
      this.timeInterval = null;
    }
    
    if (this.animationFrame) {
      cancelAnimationFrame(this.animationFrame);
      this.animationFrame = null;
    }
  }

  /**
   * Обновляет текущее время
   */
  updateTime() {
    const oldTime = this.currentTime;
    this.currentTime = new Date();
    
    // Отправляем событие обновления времени
    this.emit('time-updated', {
      time: this.currentTime,
      oldTime,
      formatted: this.getFormattedTime()
    });
  }

  /**
   * Получает отформатированное время
   */
  getFormattedTime() {
    const format = this.getSetting('format');
    const showSeconds = this.getSetting('showSeconds');
    const locale = this.getSetting('locale');
    
    const options = {
      hour12: format === '12h',
      hour: '2-digit',
      minute: '2-digit'
    };
    
    if (showSeconds) {
      options.second = '2-digit';
    }
    
    return this.currentTime.toLocaleTimeString(locale, options);
  }

  /**
   * Получает отформатированную дату
   */
  getFormattedDate() {
    const locale = this.getSetting('locale');
    
    return this.currentTime.toLocaleDateString(locale, {
      year: 'numeric',
      month: 'long',
      day: 'numeric'
    });
  }

  /**
   * Получает день недели
   */
  getFormattedDay() {
    const locale = this.getSetting('locale');
    
    return this.currentTime.toLocaleDateString(locale, {
      weekday: 'long'
    });
  }

  /**
   * Получает альтернативный текст
   */
  getAlternativeText() {
    const template = this.getSetting('alternativeText');
    const time = this.getFormattedTime();
    
    return template.replace('{time}', time);
  }

  /**
   * Получает отдельные компоненты времени
   */
  getTimeComponents() {
    const format = this.getSetting('format');
    const locale = this.getSetting('locale');
    
    const hours = this.currentTime.getHours();
    const minutes = this.currentTime.getMinutes();
    const seconds = this.currentTime.getSeconds();
    
    let displayHours = hours;
    let period = '';
    
    if (format === '12h') {
      period = hours >= 12 ? 'PM' : 'AM';
      displayHours = hours % 12 || 12;
    }
    
    return {
      hours: displayHours.toString().padStart(2, '0'),
      minutes: minutes.toString().padStart(2, '0'),
      seconds: seconds.toString().padStart(2, '0'),
      period,
      raw: {
        hours,
        minutes,
        seconds
      }
    };
  }

  /**
   * Устанавливает временную зону
   */
  setTimezone(timezone) {
    // TODO: Реализовать поддержку временных зон
    console.log(`Setting timezone to: ${timezone}`);
  }

  /**
   * Получает CSS стили для времени
   */
  getTimeStyles() {
    return this.getSetting('timeStyle');
  }

  /**
   * Получает CSS стили для даты
   */
  getDateStyles() {
    return this.getSetting('dateStyle');
  }

  /**
   * Получает CSS стили для дня недели
   */
  getDayStyles() {
    return this.getSetting('dayStyle');
  }

  /**
   * Получает настройки кастомных элементов
   */
  getCustomElements() {
    return this.getSetting('customElements');
  }

  /**
   * Проверяет, включен ли альтернативный режим
   */
  isAlternativeMode() {
    return this.getSetting('alternativeMode');
  }

  /**
   * Проверяет, нужно ли показывать секунды
   */
  shouldShowSeconds() {
    return this.getSetting('showSeconds');
  }

  /**
   * Проверяет, нужно ли показывать дату
   */
  shouldShowDate() {
    return this.getSetting('showDate');
  }

  /**
   * Проверяет, нужно ли показывать день недели
   */
  shouldShowDay() {
    return this.getSetting('showDay');
  }

  /**
   * Получает позицию виджета
   */
  getPosition() {
    return this.getSetting('position');
  }

  /**
   * Получает выравнивание виджета
   */
  getAlignment() {
    return this.getSetting('alignment');
  }

  /**
   * Включает/выключает альтернативный режим
   */
  async toggleAlternativeMode() {
    const current = this.getSetting('alternativeMode');
    await this.setSetting('alternativeMode', !current);
    this.emit('mode-changed', { alternativeMode: !current });
  }

  /**
   * Обновляет формат времени
   */
  async setTimeFormat(format) {
    await this.setSetting('format', format);
    this.emit('format-changed', { format });
  }

  /**
   * Обновляет стиль элемента времени
   */
  async updateElementStyle(element, styles) {
    const customElements = this.getSetting('customElements');
    customElements[element] = { ...customElements[element], ...styles };
    await this.setSetting('customElements', customElements);
    this.emit('style-updated', { element, styles });
  }

  /**
   * Валидирует настройки плагина
   */
  validateSettings(settings) {
    // Проверяем формат времени
    if (!['12h', '24h'].includes(settings.format)) {
      return false;
    }
    
    // Проверяем позицию
    if (!['center', 'top', 'bottom', 'left', 'right'].includes(settings.position)) {
      return false;
    }
    
    // Проверяем выравнивание
    if (!['left', 'center', 'right'].includes(settings.alignment)) {
      return false;
    }
    
    return true;
  }

  /**
   * Экспортирует настройки с текущим временем
   */
  exportSettings() {
    const baseExport = super.exportSettings();
    return {
      ...baseExport,
      currentTime: this.currentTime.toISOString(),
      timezone: Intl.DateTimeFormat().resolvedOptions().timeZone
    };
  }
}

// Создаем экземпляр плагина
export const TimePlugin = new TimePluginClass();
