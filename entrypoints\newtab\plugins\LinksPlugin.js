import { BasePlugin } from "../utils/BasePlugin.js";
import LinksComponent from "../widgets/LinksWidget.vue";
import LinksSettings from "../components/LinksSettings.vue";

/**
 * Плагин управления ссылками Veyra
 * Поддерживает favicon, иконки, папки и drag'n'drop сортировку
 */
class LinksPluginClass extends BasePlugin {
	constructor() {
		super({
			id: "links",
			name: "Ссылки",
			version: "1.0.0",
			description: "Управление ссылками с поддержкой иконок и папок",
			component: LinksComponent,
			settingsComponent: LinksSettings,
			defaultSettings: {
				// Основные настройки отображения
				displayMode: "grid", // 'grid', 'list', 'compact'
				itemsPerRow: 6,
				showLabels: true,
				showFavicons: true,

				// Форматы отображения ссылок
				linkFormat: "icon-text", // 'icon-text', 'icon-only', 'text-only'

				// Источники иконок
				iconSources: {
					favicon: true,
					simpleIcons: true,
					iconify: true,
					customSvg: true,
				},

				// Стилизация
				linkStyle: {
					width: "80px",
					height: "80px",
					borderRadius: "12px",
					backgroundColor: "rgba(255, 255, 255, 0.1)",
					backdropFilter: "blur(10px)",
					border: "1px solid rgba(255, 255, 255, 0.2)",
					padding: "12px",
					margin: "8px",
					transition: "all 0.3s ease",
				},

				labelStyle: {
					fontSize: "0.9rem",
					fontWeight: "500",
					color: "#ffffff",
					textShadow: "0 1px 3px rgba(0, 0, 0, 0.5)",
					marginTop: "8px",
					textAlign: "center",
					maxWidth: "80px",
					overflow: "hidden",
					textOverflow: "ellipsis",
					whiteSpace: "nowrap",
				},

				iconStyle: {
					width: "32px",
					height: "32px",
					filter: "drop-shadow(0 2px 4px rgba(0, 0, 0, 0.3))",
				},

				// Позиционирование
				position: "bottom", // 'top', 'center', 'bottom'
				alignment: "center", // 'left', 'center', 'right'

				// Ссылки и папки
				links: [
					{
						id: "google",
						title: "Google",
						url: "https://google.com",
						icon: {
							type: "favicon",
							url: "https://google.com/favicon.ico",
						},
						folder: null,
						order: 0,
					},
					{
						id: "youtube",
						title: "YouTube",
						url: "https://youtube.com",
						icon: {
							type: "simpleIcons",
							name: "youtube",
						},
						folder: null,
						order: 1,
					},
				],

				folders: [
					{
						id: "social",
						title: "Социальные сети",
						icon: {
							type: "iconify",
							name: "mdi:account-group",
						},
						expanded: false,
						order: 0,
					},
				],

				// Drag & Drop
				enableDragDrop: true,

				// Массовое редактирование
				bulkEditMode: false,
				selectedLinks: [],

				// Анимации
				enableAnimations: true,
				hoverEffect: "scale", // 'scale', 'glow', 'lift', 'none'
				entranceAnimation: "fadeInUp", // 'fadeInUp', 'slideUp', 'scale', 'none'
			},
		});

		this.draggedItem = null;
		this.faviconCache = new Map();
	}

	async onInit() {
		// Загружаем иконки для ссылок
		await this.loadLinkIcons();

		// Инициализируем кэш favicon'ов
		this.initFaviconCache();

		console.log("Links plugin initialized");
	}

	async onDestroy() {
		// Очищаем кэш
		this.faviconCache.clear();
		console.log("Links plugin destroyed");
	}

	/**
	 * Получает все ссылки
	 */
	getLinks() {
		return this.getSetting("links") || [];
	}

	/**
	 * Получает все папки
	 */
	getFolders() {
		return this.getSetting("folders") || [];
	}

	/**
	 * Получает ссылки в определенной папке
	 */
	getLinksInFolder(folderId) {
		return this.getLinks().filter((link) => link.folder === folderId);
	}

	/**
	 * Получает ссылки без папки
	 */
	getLinksWithoutFolder() {
		return this.getLinks().filter((link) => !link.folder);
	}

	/**
	 * Добавляет новую ссылку
	 */
	async addLink(linkData) {
		const links = this.getLinks();
		const newLink = {
			id: this.generateId(),
			title: linkData.title,
			url: linkData.url,
			icon: linkData.icon || {
				type: "favicon",
				url: this.getFaviconUrl(linkData.url),
			},
			folder: linkData.folder || null,
			order: links.length,
			...linkData,
		};

		links.push(newLink);
		await this.setSetting("links", links);

		// Загружаем иконку для новой ссылки
		await this.loadIconForLink(newLink);

		this.emit("link-added", { link: newLink });
		return newLink;
	}

	/**
	 * Обновляет ссылку
	 */
	async updateLink(linkId, updates) {
		const links = this.getLinks();
		const linkIndex = links.findIndex((link) => link.id === linkId);

		if (linkIndex === -1) {
			throw new Error(`Link with id ${linkId} not found`);
		}

		links[linkIndex] = { ...links[linkIndex], ...updates };
		await this.setSetting("links", links);

		// Перезагружаем иконку если изменилась
		if (updates.icon || updates.url) {
			await this.loadIconForLink(links[linkIndex]);
		}

		this.emit("link-updated", { link: links[linkIndex] });
		return links[linkIndex];
	}

	/**
	 * Удаляет ссылку
	 */
	async removeLink(linkId) {
		const links = this.getLinks();
		const linkIndex = links.findIndex((link) => link.id === linkId);

		if (linkIndex === -1) {
			throw new Error(`Link with id ${linkId} not found`);
		}

		const removedLink = links.splice(linkIndex, 1)[0];
		await this.setSetting("links", links);

		this.emit("link-removed", { link: removedLink });
		return removedLink;
	}

	/**
	 * Добавляет новую папку
	 */
	async addFolder(folderData) {
		const folders = this.getFolders();
		const newFolder = {
			id: this.generateId(),
			title: folderData.title,
			icon: folderData.icon || { type: "iconify", name: "mdi:folder" },
			expanded: false,
			order: folders.length,
			...folderData,
		};

		folders.push(newFolder);
		await this.setSetting("folders", folders);

		this.emit("folder-added", { folder: newFolder });
		return newFolder;
	}

	/**
	 * Обновляет папку
	 */
	async updateFolder(folderId, updates) {
		const folders = this.getFolders();
		const folderIndex = folders.findIndex((folder) => folder.id === folderId);

		if (folderIndex === -1) {
			throw new Error(`Folder with id ${folderId} not found`);
		}

		folders[folderIndex] = { ...folders[folderIndex], ...updates };
		await this.setSetting("folders", folders);

		this.emit("folder-updated", { folder: folders[folderIndex] });
		return folders[folderIndex];
	}

	/**
	 * Удаляет папку
	 */
	async removeFolder(folderId) {
		const folders = this.getFolders();
		const folderIndex = folders.findIndex((folder) => folder.id === folderId);

		if (folderIndex === -1) {
			throw new Error(`Folder with id ${folderId} not found`);
		}

		// Перемещаем все ссылки из папки в корень
		const links = this.getLinks();
		const updatedLinks = links.map((link) =>
			link.folder === folderId ? { ...link, folder: null } : link
		);
		await this.setSetting("links", updatedLinks);

		const removedFolder = folders.splice(folderIndex, 1)[0];
		await this.setSetting("folders", folders);

		this.emit("folder-removed", { folder: removedFolder });
		return removedFolder;
	}

	/**
	 * Переключает состояние папки (развернута/свернута)
	 */
	async toggleFolder(folderId) {
		const folders = this.getFolders();
		const folder = folders.find((f) => f.id === folderId);

		if (folder) {
			folder.expanded = !folder.expanded;
			await this.setSetting("folders", folders);
			this.emit("folder-toggled", { folder });
		}
	}

	/**
	 * Перемещает ссылку в папку
	 */
	async moveLinkToFolder(linkId, folderId) {
		await this.updateLink(linkId, { folder: folderId });
	}

	/**
	 * Изменяет порядок ссылок
	 */
	async reorderLinks(linkIds) {
		const links = this.getLinks();
		const reorderedLinks = linkIds.map((id, index) => {
			const link = links.find((l) => l.id === id);
			return { ...link, order: index };
		});

		await this.setSetting("links", reorderedLinks);
		this.emit("links-reordered", { links: reorderedLinks });
	}

	/**
	 * Загружает иконки для всех ссылок
	 */
	async loadLinkIcons() {
		const links = this.getLinks();

		for (const link of links) {
			await this.loadIconForLink(link);
		}
	}

	/**
	 * Загружает иконку для конкретной ссылки
	 */
	async loadIconForLink(link) {
		try {
			switch (link.icon.type) {
				case "favicon":
					await this.loadFavicon(link);
					break;
				case "simpleIcons":
					await this.loadSimpleIcon(link);
					break;
				case "iconify":
					await this.loadIconifyIcon(link);
					break;
				case "customSvg":
					// Кастомные SVG уже содержат данные
					break;
			}
		} catch (error) {
			console.error(`Failed to load icon for link ${link.id}:`, error);
			// Fallback к favicon
			link.icon = { type: "favicon", url: this.getFaviconUrl(link.url) };
		}
	}

	/**
	 * Загружает favicon
	 */
	async loadFavicon(link) {
		const faviconUrl = link.icon.url || this.getFaviconUrl(link.url);

		if (this.faviconCache.has(faviconUrl)) {
			link.icon.data = this.faviconCache.get(faviconUrl);
			return;
		}

		try {
			// Проверяем доступность favicon
			const response = await fetch(faviconUrl, { method: "HEAD" });
			if (response.ok) {
				link.icon.url = faviconUrl;
				this.faviconCache.set(faviconUrl, faviconUrl);
			}
		} catch (error) {
			// Используем Google favicon service как fallback
			const fallbackUrl = `https://www.google.com/s2/favicons?domain=${
				new URL(link.url).hostname
			}&sz=32`;
			link.icon.url = fallbackUrl;
			this.faviconCache.set(faviconUrl, fallbackUrl);
		}
	}

	/**
	 * Загружает иконку из Simple Icons
	 */
	async loadSimpleIcon(link) {
		// TODO: Реализовать загрузку из Simple Icons
		console.log(`Loading Simple Icon: ${link.icon.name}`);
	}

	/**
	 * Загружает иконку из Iconify
	 */
	async loadIconifyIcon(link) {
		// TODO: Реализовать загрузку из Iconify
		console.log(`Loading Iconify icon: ${link.icon.name}`);
	}

	/**
	 * Получает URL favicon для домена
	 */
	getFaviconUrl(url) {
		try {
			const domain = new URL(url).hostname;
			return `https://${domain}/favicon.ico`;
		} catch (error) {
			return 'data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" width="32" height="32"><rect width="32" height="32" fill="%23ddd"/></svg>';
		}
	}

	/**
	 * Инициализирует кэш favicon'ов
	 */
	initFaviconCache() {
		// Загружаем кэш из storage
		chrome.storage.local.get("faviconCache").then((result) => {
			if (result.faviconCache) {
				this.faviconCache = new Map(Object.entries(result.faviconCache));
			}
		});
	}

	/**
	 * Сохраняет кэш favicon'ов
	 */
	async saveFaviconCache() {
		try {
			const cacheObject = Object.fromEntries(this.faviconCache);
			await chrome.storage.local.set({ faviconCache: cacheObject });
		} catch (error) {
			console.error("Failed to save favicon cache:", error);
		}
	}

	/**
	 * Генерирует уникальный ID
	 */
	generateId() {
		return Date.now().toString(36) + Math.random().toString(36).substr(2);
	}

	/**
	 * Получает настройки отображения
	 */
	getDisplaySettings() {
		return {
			displayMode: this.getSetting("displayMode"),
			itemsPerRow: this.getSetting("itemsPerRow"),
			showLabels: this.getSetting("showLabels"),
			showFavicons: this.getSetting("showFavicons"),
			linkFormat: this.getSetting("linkFormat"),
		};
	}

	/**
	 * Получает стили элементов
	 */
	getStyles() {
		return {
			link: this.getSetting("linkStyle"),
			label: this.getSetting("labelStyle"),
			icon: this.getSetting("iconStyle"),
		};
	}

	/**
	 * Валидирует настройки плагина
	 */
	validateSettings(settings) {
		// Проверяем массивы ссылок и папок
		if (!Array.isArray(settings.links) || !Array.isArray(settings.folders)) {
			return false;
		}

		// Проверяем режим отображения
		if (!["grid", "list", "compact"].includes(settings.displayMode)) {
			return false;
		}

		return true;
	}
}

// Создаем экземпляр плагина
export const LinksPlugin = new LinksPluginClass();
