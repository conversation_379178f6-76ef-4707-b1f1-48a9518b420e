<template>
  <div 
    class="time-widget"
    :class="[
      `position-${position}`,
      `align-${alignment}`,
      { 'alternative-mode': isAlternativeMode }
    ]"
  >
    <!-- Альтернативный режим -->
    <div v-if="isAlternativeMode" class="alternative-text" :style="timeStyles">
      {{ alternativeText }}
    </div>
    
    <!-- Обычный режим -->
    <div v-else class="time-display">
      <!-- День недели -->
      <div 
        v-if="shouldShowDay" 
        class="day-display"
        :style="dayStyles"
      >
        {{ formattedDay }}
      </div>
      
      <!-- Время -->
      <div class="time-container">
        <!-- Кастомные элементы времени -->
        <div v-if="useCustomElements" class="custom-time">
          <span 
            v-if="customElements.hours.enabled"
            class="time-hours"
            :style="getElementStyle('hours')"
          >
            {{ timeComponents.hours }}
          </span>
          
          <span 
            v-if="customElements.separator.enabled"
            class="time-separator"
            :class="{ 'animate-blink': customElements.separator.animation === 'blink' }"
            :style="getElementStyle('separator')"
          >
            {{ customElements.separator.character }}
          </span>
          
          <span 
            v-if="customElements.minutes.enabled"
            class="time-minutes"
            :style="getElementStyle('minutes')"
          >
            {{ timeComponents.minutes }}
          </span>
          
          <span 
            v-if="customElements.seconds.enabled && shouldShowSeconds"
            class="time-seconds"
            :class="`seconds-${customElements.seconds.position}`"
            :style="getElementStyle('seconds')"
          >
            {{ timeComponents.seconds }}
          </span>
          
          <span 
            v-if="timeComponents.period"
            class="time-period"
            :style="timeStyles"
          >
            {{ timeComponents.period }}
          </span>
        </div>
        
        <!-- Стандартное отображение времени -->
        <div v-else class="standard-time" :style="timeStyles">
          {{ formattedTime }}
        </div>
      </div>
      
      <!-- Дата -->
      <div 
        v-if="shouldShowDate" 
        class="date-display"
        :style="dateStyles"
      >
        {{ formattedDate }}
      </div>
    </div>
    
    <!-- Эффект свечения -->
    <div 
      v-if="glowEffect" 
      class="glow-effect"
      :style="{ 
        boxShadow: `0 0 30px ${glowColor}, 0 0 60px ${glowColor}`,
        opacity: 0.6
      }"
    ></div>
    
    <!-- Фоновый блюр -->
    <div 
      v-if="backgroundBlur" 
      class="background-blur"
      :style="{ 
        backgroundColor: `rgba(255, 255, 255, ${backgroundOpacity})`,
        backdropFilter: 'blur(10px)'
      }"
    ></div>
  </div>
</template>

<script setup>
import { computed, ref, onMounted, onUnmounted } from 'vue';
import { pluginSystem } from '../utils/pluginSystem.js';

const props = defineProps({
  pluginId: {
    type: String,
    required: true
  }
});

// Получаем плагин
const plugin = computed(() => pluginSystem.getPlugin(props.pluginId));

// Реактивные данные
const currentTime = ref(new Date());
const formattedTime = ref('');
const formattedDate = ref('');
const formattedDay = ref('');
const timeComponents = ref({});
const alternativeText = ref('');

// Настройки из плагина
const isAlternativeMode = computed(() => plugin.value?.isAlternativeMode() || false);
const shouldShowSeconds = computed(() => plugin.value?.shouldShowSeconds() || false);
const shouldShowDate = computed(() => plugin.value?.shouldShowDate() || true);
const shouldShowDay = computed(() => plugin.value?.shouldShowDay() || true);
const position = computed(() => plugin.value?.getPosition() || 'center');
const alignment = computed(() => plugin.value?.getAlignment() || 'center');

// Стили
const timeStyles = computed(() => plugin.value?.getTimeStyles() || {});
const dateStyles = computed(() => plugin.value?.getDateStyles() || {});
const dayStyles = computed(() => plugin.value?.getDayStyles() || {});

// Кастомные элементы
const customElements = computed(() => plugin.value?.getCustomElements() || {});
const useCustomElements = computed(() => {
  const elements = customElements.value;
  return elements && (elements.hours?.enabled || elements.minutes?.enabled);
});

// Эффекты
const glowEffect = computed(() => plugin.value?.getSetting('glowEffect') || false);
const glowColor = computed(() => plugin.value?.getSetting('glowColor') || '#8B45C7');
const backgroundBlur = computed(() => plugin.value?.getSetting('backgroundBlur') || false);
const backgroundOpacity = computed(() => plugin.value?.getSetting('backgroundOpacity') || 0.1);

// Методы
const updateTimeData = () => {
  if (!plugin.value) return;
  
  formattedTime.value = plugin.value.getFormattedTime();
  formattedDate.value = plugin.value.getFormattedDate();
  formattedDay.value = plugin.value.getFormattedDay();
  timeComponents.value = plugin.value.getTimeComponents();
  alternativeText.value = plugin.value.getAlternativeText();
};

const getElementStyle = (element) => {
  const elements = customElements.value;
  if (!elements || !elements[element]) return {};
  
  const elementConfig = elements[element];
  return {
    fontSize: elementConfig.fontSize,
    fontWeight: elementConfig.fontWeight,
    color: elementConfig.color,
    fontFamily: elementConfig.fontFamily || 'Onest'
  };
};

// Слушатели событий плагина
const onTimeUpdated = () => {
  updateTimeData();
};

const onSettingsUpdated = () => {
  updateTimeData();
};

onMounted(() => {
  if (plugin.value) {
    updateTimeData();
    
    // Подписываемся на события плагина
    plugin.value.addEventListener('time-updated', onTimeUpdated);
    plugin.value.addEventListener('settings-updated', onSettingsUpdated);
  }
});

onUnmounted(() => {
  if (plugin.value) {
    // Отписываемся от событий
    plugin.value.removeEventListener('time-updated', onTimeUpdated);
    plugin.value.removeEventListener('settings-updated', onSettingsUpdated);
  }
});
</script>

<style scoped>
.time-widget {
  position: relative;
  display: flex;
  flex-direction: column;
  user-select: none;
  z-index: 10;
}

/* Позиционирование */
.position-center {
  align-items: center;
  justify-content: center;
  text-align: center;
}

.position-top {
  align-items: flex-start;
  justify-content: flex-start;
  position: absolute;
  top: 20px;
  left: 50%;
  transform: translateX(-50%);
}

.position-bottom {
  align-items: flex-end;
  justify-content: flex-end;
  position: absolute;
  bottom: 20px;
  left: 50%;
  transform: translateX(-50%);
}

.position-left {
  align-items: flex-start;
  justify-content: center;
  position: absolute;
  left: 20px;
  top: 50%;
  transform: translateY(-50%);
}

.position-right {
  align-items: flex-end;
  justify-content: center;
  position: absolute;
  right: 20px;
  top: 50%;
  transform: translateY(-50%);
}

/* Выравнивание */
.align-left {
  text-align: left;
}

.align-center {
  text-align: center;
}

.align-right {
  text-align: right;
}

/* Отображение времени */
.time-display {
  display: flex;
  flex-direction: column;
  align-items: inherit;
}

.time-container {
  position: relative;
  display: flex;
  align-items: baseline;
}

.custom-time {
  display: flex;
  align-items: baseline;
  position: relative;
}

.time-seconds {
  position: relative;
}

.seconds-top-right {
  position: absolute;
  top: -0.5em;
  right: -1em;
  font-size: 0.6em !important;
}

.seconds-bottom-right {
  position: absolute;
  bottom: -0.5em;
  right: -1em;
  font-size: 0.6em !important;
}

.seconds-inline {
  margin-left: 0.2em;
}

/* Анимации */
.animate-blink {
  animation: blink 1s infinite;
}

@keyframes blink {
  0%, 50% { opacity: 1; }
  51%, 100% { opacity: 0.3; }
}

/* Эффекты */
.glow-effect {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  pointer-events: none;
  border-radius: 10px;
}

.background-blur {
  position: absolute;
  top: -20px;
  left: -20px;
  right: -20px;
  bottom: -20px;
  border-radius: 15px;
  z-index: -1;
}

/* Альтернативный режим */
.alternative-mode .alternative-text {
  font-style: italic;
  text-align: center;
}

/* Адаптивность */
@media (max-width: 768px) {
  .time-widget {
    font-size: 0.8em;
  }
  
  .position-left,
  .position-right {
    position: relative;
    transform: none;
    left: auto;
    right: auto;
    top: auto;
  }
}

/* Поддержка prefers-reduced-motion */
@media (prefers-reduced-motion: reduce) {
  .animate-blink {
    animation: none;
  }
  
  .time-separator {
    opacity: 1 !important;
  }
}
</style>
