<template>
	<div class="weather-widget widget">
		<div v-if="weatherData" class="weather-content">
			<!-- Основная информация -->
			<div class="weather-main">
				<div class="temperature">{{ Math.round(weatherData.main.temp) }}°C</div>
				<div class="weather-description">
					{{ weatherData.weather[0].description }}
				</div>
			</div>

			<!-- Детали погоды -->
			<div class="weather-details">
				<div class="detail-item">
					<Thermometer class="detail-icon" />
					<span class="detail-label">{{ $t("weather.feelsLike") }}</span>
					<span class="detail-value"
						>{{ Math.round(weatherData.main.feels_like) }}°C</span
					>
				</div>

				<div class="detail-item">
					<Droplets class="detail-icon" />
					<span class="detail-label">{{ $t("weather.humidity") }}</span>
					<span class="detail-value">{{ weatherData.main.humidity }}%</span>
				</div>

				<div class="detail-item">
					<Wind class="detail-icon" />
					<span class="detail-label">{{ $t("weather.wind") }}</span>
					<span class="detail-value"
						>{{ Math.round(weatherData.wind.speed) }} м/с</span
					>
				</div>

				<div class="detail-item">
					<MapPin class="detail-icon" />
					<span class="detail-label">{{ $t("weather.location") }}</span>
					<span class="detail-value">{{ weatherData.name }}</span>
				</div>
			</div>

			<!-- Кнопка обновления -->
			<button @click="refreshWeather" class="refresh-btn" :disabled="isLoading">
				<RefreshCw class="refresh-icon" :class="{ spinning: isLoading }" />
				{{ $t("weather.refresh") }}
			</button>
		</div>

		<div v-else-if="error" class="weather-error">
			<AlertCircle class="error-icon" />
			<p class="error-message">{{ error }}</p>
			<button @click="refreshWeather" class="btn btn-primary">
				{{ $t("weather.refresh") }}
			</button>
		</div>

		<div v-else-if="!apiKey" class="weather-placeholder">
			<Cloud class="placeholder-icon" />
			<p>{{ $t("weather.apiKeyRequired") }}</p>
			<p class="placeholder-hint">{{ $t("weather.locationRequired") }}</p>
		</div>

		<div v-else class="weather-placeholder">
			<Cloud class="placeholder-icon" />
			<p>{{ $t("weather.noData") }}</p>
			<button @click="refreshWeather" class="btn btn-primary">
				{{ $t("weather.refresh") }}
			</button>
		</div>
	</div>
</template>

<script setup>
import { ref, onMounted, computed } from "vue";
import { useSettingsStore } from "../stores/settingsStore";
import {
	RefreshCw,
	AlertCircle,
	Thermometer,
	Droplets,
	Wind,
	MapPin,
	Cloud,
} from "lucide-vue-next";

const settingsStore = useSettingsStore();

// Реактивные данные
const weatherData = ref(null);
const isLoading = ref(false);
const error = ref(null);

// Вычисляемые свойства
const apiKey = computed(() => settingsStore.settings.openWeatherApiKey);
const location = computed(() => settingsStore.settings.weatherLocation);

// Методы
const getWeatherIcon = (iconCode) => {
	return `https://openweathermap.org/img/wn/${iconCode}@2x.png`;
};

const formatTime = (timestamp) => {
	return new Date(timestamp * 1000).toLocaleTimeString();
};

const fetchWeather = async () => {
	if (!apiKey.value) {
		error.value =
			settingsStore.settings.language === "ru"
				? "API ключ не настроен"
				: "API key not configured";
		return;
	}

	isLoading.value = true;
	error.value = null;

	try {
		const response = await fetch(
			`https://api.openweathermap.org/data/2.5/weather?q=${encodeURIComponent(
				location.value
			)}&appid=${apiKey.value}&units=metric&lang=${
				settingsStore.settings.language === "ru" ? "ru" : "en"
			}`
		);

		if (!response.ok) {
			throw new Error(`HTTP error! status: ${response.status}`);
		}

		const data = await response.json();
		weatherData.value = data;
	} catch (err) {
		console.error("Ошибка при получении погоды:", err);
		error.value =
			settingsStore.settings.language === "ru"
				? "Не удалось загрузить данные о погоде"
				: "Failed to load weather data";
	} finally {
		isLoading.value = false;
	}
};

const refreshWeather = () => {
	fetchWeather();
};

// Жизненный цикл
onMounted(() => {
	if (apiKey.value) {
		fetchWeather();
	}
});
</script>

<style scoped>
.weather-widget {
	padding: var(--spacing-lg);
	background: rgba(255, 255, 255, 0.1);
	border: 1px solid rgba(255, 255, 255, 0.2);
	border-radius: var(--border-radius);
	backdrop-filter: blur(10px);
	min-height: 200px;
}

.weather-content {
	text-align: center;
}

.weather-main {
	margin-bottom: var(--spacing-lg);
}

.temperature {
	font-size: 3rem;
	font-weight: 700;
	color: var(--text-primary);
	margin-bottom: var(--spacing-sm);
	text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.3);
}

.weather-description {
	font-size: 1.2rem;
	color: var(--text-secondary);
	text-transform: capitalize;
}

.weather-details {
	display: grid;
	grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
	gap: var(--spacing-md);
	margin-bottom: var(--spacing-lg);
}

.detail-item {
	display: flex;
	flex-direction: column;
	align-items: center;
	gap: var(--spacing-xs);
	padding: var(--spacing-sm);
	background: rgba(255, 255, 255, 0.05);
	border-radius: var(--border-radius);
}

.detail-icon {
	width: 24px;
	height: 24px;
	color: var(--accent-color);
}

.detail-label {
	font-size: 0.8rem;
	color: var(--text-muted);
	text-align: center;
}

.detail-value {
	font-size: 1rem;
	font-weight: 600;
	color: var(--text-primary);
}

.refresh-btn {
	background: var(--accent-color);
	color: white;
	border: none;
	border-radius: var(--border-radius);
	padding: var(--spacing-sm) var(--spacing-md);
	cursor: pointer;
	display: flex;
	align-items: center;
	gap: var(--spacing-sm);
	margin: 0 auto;
	transition: all 0.2s ease;
}

.refresh-btn:hover:not(:disabled) {
	background: var(--accent-color-hover);
	transform: translateY(-1px);
}

.refresh-btn:disabled {
	opacity: 0.6;
	cursor: not-allowed;
}

.refresh-icon {
	width: 18px;
	height: 18px;
}

.refresh-icon.spinning {
	animation: spin 1s linear infinite;
}

@keyframes spin {
	from {
		transform: rotate(0deg);
	}
	to {
		transform: rotate(360deg);
	}
}

.weather-error,
.weather-placeholder {
	text-align: center;
	padding: var(--spacing-xl) 0;
}

.error-icon,
.placeholder-icon {
	width: 48px;
	height: 48px;
	color: var(--text-muted);
	margin: 0 auto var(--spacing-md) auto;
}

.error-message {
	color: var(--danger-color);
	margin-bottom: var(--spacing-md);
}

.placeholder-hint {
	color: var(--text-muted);
	font-size: 0.9rem;
	margin-top: var(--spacing-sm);
}

.btn {
	padding: var(--spacing-sm) var(--spacing-md);
	border: none;
	border-radius: var(--border-radius);
	cursor: pointer;
	font-size: 0.9em;
	font-weight: 500;
	transition: all 0.2s ease;
}

.btn-primary {
	background: var(--accent-color);
	color: white;
}

.btn-primary:hover {
	background: var(--accent-color-hover);
}

/* Адаптивность */
@media (max-width: 768px) {
	.temperature {
		font-size: 2.5rem;
	}

	.weather-details {
		grid-template-columns: repeat(2, 1fr);
	}
}

@media (max-width: 480px) {
	.temperature {
		font-size: 2rem;
	}

	.weather-description {
		font-size: 1rem;
	}

	.weather-details {
		grid-template-columns: 1fr;
	}
}
</style>
