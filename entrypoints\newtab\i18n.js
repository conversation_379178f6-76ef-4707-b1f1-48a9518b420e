import { createI18n } from "vue-i18n";
import en from "./locales/en.json";
import ru from "./locales/ru.json";

const messages = { en, ru };

// Получаем язык из localStorage или используем язык браузера
const getInitialLocale = () => {
	const savedLang = localStorage.getItem("veyra-lang");
	if (savedLang && messages[savedLang]) {
		return savedLang;
	}

	const browserLang = navigator.language.startsWith("ru") ? "ru" : "en";
	return messages[browserLang] ? browserLang : "en";
};

export const i18n = createI18n({
	locale: getInitialLocale(),
	fallbackLocale: "en",
	legacy: false,
	messages,
});

// Функция для динамического изменения языка
export const setLocale = (locale) => {
	if (messages[locale]) {
		i18n.global.locale.value = locale;
		localStorage.setItem("veyra-lang", locale);
	}
};
