<template>
	<div class="time-widget widget">
		<!-- Основное время -->
		<div class="time-main">
			<div class="time-display">
				{{ time }}
				<span v-if="showSeconds" class="seconds">:{{ seconds }}</span>
			</div>

			<!-- Дата -->
			<div v-if="showDate" class="date-display">
				{{ date }}
			</div>

			<!-- Часовой пояс -->
			<div class="timezone-display">
				{{ timezone }}
			</div>
		</div>
	</div>
</template>

<script setup>
import { ref, onMounted, onUnmounted, computed } from "vue";
import { useSettingsStore } from "../stores/settingsStore";

const settingsStore = useSettingsStore();

// Реактивные данные
const currentTime = ref(new Date());
const currentSeconds = ref(new Date().getSeconds());
let timeInterval = null;

// Вычисляемые свойства
const language = computed(() => settingsStore.settings.language);
const showSeconds = computed(() => settingsStore.settings.showSeconds);
const showDate = computed(() => settingsStore.settings.showDate);

const time = computed(() => {
	return currentTime.value.toLocaleTimeString(language.value, {
		hour: "2-digit",
		minute: "2-digit",
		hour12: false,
	});
});

const seconds = computed(() => {
	return currentSeconds.value.toString().padStart(2, "0");
});

const date = computed(() => {
	return currentTime.value.toLocaleDateString(language.value, {
		weekday: "long",
		year: "numeric",
		month: "long",
		day: "numeric",
	});
});

const timezone = computed(() => {
	return Intl.DateTimeFormat().resolvedOptions().timeZone;
});

// Методы
const updateTime = () => {
	const now = new Date();
	currentTime.value = now;
	currentSeconds.value = now.getSeconds();
};

// Жизненный цикл
onMounted(() => {
	updateTime();
	timeInterval = setInterval(updateTime, 1000);
});

onUnmounted(() => {
	if (timeInterval) {
		clearInterval(timeInterval);
	}
});
</script>

<style scoped>
.time-widget {
	display: flex;
	flex-direction: column;
	align-items: center;
	justify-content: center;
	padding: var(--spacing-lg);
	background: rgba(255, 255, 255, 0.1);
	border: 1px solid rgba(255, 255, 255, 0.2);
	border-radius: var(--border-radius);
	backdrop-filter: blur(10px);
	min-height: 200px;
}

.time-main {
	text-align: center;
}

.time-display {
	font-size: 4rem;
	font-weight: 700;
	color: var(--text-primary);
	margin-bottom: var(--spacing-md);
	text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.3);
}

.seconds {
	font-size: 2rem;
	color: var(--text-secondary);
	opacity: 0.8;
}

.date-display {
	font-size: 1.2rem;
	color: var(--text-secondary);
	margin-bottom: var(--spacing-sm);
	text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.3);
}

.timezone-display {
	font-size: 0.9rem;
	color: var(--text-muted);
	opacity: 0.7;
}

/* Адаптивность */
@media (max-width: 768px) {
	.time-display {
		font-size: 3rem;
	}

	.seconds {
		font-size: 1.5rem;
	}

	.date-display {
		font-size: 1rem;
	}
}

@media (max-width: 480px) {
	.time-display {
		font-size: 2.5rem;
	}

	.seconds {
		font-size: 1.2rem;
	}

	.date-display {
		font-size: 0.9rem;
	}
}
</style>
