/**
 * Система плагинов Veyra
 * Обеспечивает регистрацию, загрузку и управление плагинами
 */

class PluginSystem {
  constructor() {
    this.plugins = new Map();
    this.activePlugins = new Set();
    this.pluginData = new Map();
    this.eventBus = new EventTarget();
  }

  /**
   * Регистрирует новый плагин
   * @param {Object} plugin - Объект плагина
   * @param {string} plugin.id - Уникальный идентификатор плагина
   * @param {string} plugin.name - Название плагина
   * @param {string} plugin.version - Версия плагина
   * @param {Function} plugin.component - Vue компонент плагина
   * @param {Function} plugin.settingsComponent - Компонент настроек плагина
   * @param {Object} plugin.defaultSettings - Настройки по умолчанию
   * @param {Function} plugin.init - Функция инициализации плагина
   * @param {Function} plugin.destroy - Функция очистки плагина
   */
  registerPlugin(plugin) {
    try {
      if (!plugin.id || !plugin.name || !plugin.component) {
        throw new Error('Plugin must have id, name, and component properties');
      }

      if (this.plugins.has(plugin.id)) {
        console.warn(`Plugin ${plugin.id} is already registered`);
        return false;
      }

      // Валидация плагина
      this.validatePlugin(plugin);

      this.plugins.set(plugin.id, {
        ...plugin,
        registered: true,
        active: false
      });

      console.log(`Plugin ${plugin.id} registered successfully`);
      this.emit('plugin:registered', { pluginId: plugin.id });
      return true;
    } catch (error) {
      console.error(`Failed to register plugin ${plugin.id}:`, error);
      return false;
    }
  }

  /**
   * Валидирует структуру плагина
   */
  validatePlugin(plugin) {
    const requiredFields = ['id', 'name', 'component'];
    const optionalFields = ['version', 'settingsComponent', 'defaultSettings', 'init', 'destroy'];
    
    for (const field of requiredFields) {
      if (!plugin[field]) {
        throw new Error(`Plugin missing required field: ${field}`);
      }
    }

    if (plugin.component && typeof plugin.component !== 'object') {
      throw new Error('Plugin component must be a Vue component object');
    }

    if (plugin.init && typeof plugin.init !== 'function') {
      throw new Error('Plugin init must be a function');
    }

    if (plugin.destroy && typeof plugin.destroy !== 'function') {
      throw new Error('Plugin destroy must be a function');
    }
  }

  /**
   * Активирует плагин
   */
  async activatePlugin(pluginId) {
    try {
      const plugin = this.plugins.get(pluginId);
      if (!plugin) {
        throw new Error(`Plugin ${pluginId} not found`);
      }

      if (plugin.active) {
        console.warn(`Plugin ${pluginId} is already active`);
        return true;
      }

      // Инициализация плагина
      if (plugin.init) {
        await plugin.init();
      }

      // Загрузка настроек плагина
      const settings = await this.loadPluginSettings(pluginId);
      this.pluginData.set(pluginId, settings);

      plugin.active = true;
      this.activePlugins.add(pluginId);

      console.log(`Plugin ${pluginId} activated successfully`);
      this.emit('plugin:activated', { pluginId });
      return true;
    } catch (error) {
      console.error(`Failed to activate plugin ${pluginId}:`, error);
      return false;
    }
  }

  /**
   * Деактивирует плагин
   */
  async deactivatePlugin(pluginId) {
    try {
      const plugin = this.plugins.get(pluginId);
      if (!plugin) {
        throw new Error(`Plugin ${pluginId} not found`);
      }

      if (!plugin.active) {
        console.warn(`Plugin ${pluginId} is not active`);
        return true;
      }

      // Очистка плагина
      if (plugin.destroy) {
        await plugin.destroy();
      }

      plugin.active = false;
      this.activePlugins.delete(pluginId);
      this.pluginData.delete(pluginId);

      console.log(`Plugin ${pluginId} deactivated successfully`);
      this.emit('plugin:deactivated', { pluginId });
      return true;
    } catch (error) {
      console.error(`Failed to deactivate plugin ${pluginId}:`, error);
      return false;
    }
  }

  /**
   * Получает список всех плагинов
   */
  getPlugins() {
    return Array.from(this.plugins.values());
  }

  /**
   * Получает список активных плагинов
   */
  getActivePlugins() {
    return Array.from(this.activePlugins).map(id => this.plugins.get(id));
  }

  /**
   * Получает плагин по ID
   */
  getPlugin(pluginId) {
    return this.plugins.get(pluginId);
  }

  /**
   * Получает настройки плагина
   */
  getPluginSettings(pluginId) {
    return this.pluginData.get(pluginId) || {};
  }

  /**
   * Обновляет настройки плагина
   */
  async updatePluginSettings(pluginId, settings) {
    try {
      const plugin = this.plugins.get(pluginId);
      if (!plugin) {
        throw new Error(`Plugin ${pluginId} not found`);
      }

      const currentSettings = this.pluginData.get(pluginId) || {};
      const newSettings = { ...currentSettings, ...settings };
      
      this.pluginData.set(pluginId, newSettings);
      await this.savePluginSettings(pluginId, newSettings);

      this.emit('plugin:settings-updated', { pluginId, settings: newSettings });
      return true;
    } catch (error) {
      console.error(`Failed to update settings for plugin ${pluginId}:`, error);
      return false;
    }
  }

  /**
   * Загружает настройки плагина из storage
   */
  async loadPluginSettings(pluginId) {
    try {
      const plugin = this.plugins.get(pluginId);
      const result = await chrome.storage.sync.get(`plugin_${pluginId}`);
      const settings = result[`plugin_${pluginId}`] || plugin.defaultSettings || {};
      return settings;
    } catch (error) {
      console.error(`Failed to load settings for plugin ${pluginId}:`, error);
      return plugin.defaultSettings || {};
    }
  }

  /**
   * Сохраняет настройки плагина в storage
   */
  async savePluginSettings(pluginId, settings) {
    try {
      await chrome.storage.sync.set({ [`plugin_${pluginId}`]: settings });
    } catch (error) {
      console.error(`Failed to save settings for plugin ${pluginId}:`, error);
    }
  }

  /**
   * Отправляет событие
   */
  emit(eventName, data) {
    this.eventBus.dispatchEvent(new CustomEvent(eventName, { detail: data }));
  }

  /**
   * Подписывается на событие
   */
  on(eventName, callback) {
    this.eventBus.addEventListener(eventName, callback);
  }

  /**
   * Отписывается от события
   */
  off(eventName, callback) {
    this.eventBus.removeEventListener(eventName, callback);
  }

  /**
   * Инициализирует систему плагинов
   */
  async init() {
    try {
      // Загружаем список активных плагинов из storage
      const result = await chrome.storage.sync.get('activePlugins');
      const activePlugins = result.activePlugins || [];

      // Активируем сохраненные плагины
      for (const pluginId of activePlugins) {
        if (this.plugins.has(pluginId)) {
          await this.activatePlugin(pluginId);
        }
      }

      console.log('Plugin system initialized');
      this.emit('system:initialized');
    } catch (error) {
      console.error('Failed to initialize plugin system:', error);
    }
  }

  /**
   * Сохраняет список активных плагинов
   */
  async saveActivePlugins() {
    try {
      const activePlugins = Array.from(this.activePlugins);
      await chrome.storage.sync.set({ activePlugins });
    } catch (error) {
      console.error('Failed to save active plugins:', error);
    }
  }
}

// Создаем глобальный экземпляр системы плагинов
export const pluginSystem = new PluginSystem();
export default pluginSystem;
