{"settings": {"title": "Settings", "general": "General", "showTime": "Show Time", "showLinks": "Show Links", "language": "Language", "background": "Background", "backgroundType": "Background Type", "gradient": "Gradient", "solid": "Solid Color", "image": "Image", "solidColor": "Background Color", "gradientType": "Gradient Type", "linear": "Linear", "radial": "Radial", "gradientAngle": "<PERSON><PERSON><PERSON>", "gradientColors": "Gradient Colors", "addColor": "Add Color", "removeColor": "Remove Color", "gradientAnimation": "Gradient Animation", "gradientSpeed": "Animation Speed", "gradientTiming": "Timing Function", "backgroundImage": "Background Image", "backgroundBlur": "Background Blur", "backgroundOpacity": "Background Opacity", "borderRadius": "Border Radius", "small": "Small", "medium": "Medium", "large": "Large", "shadows": "Shadows", "animations": "Animations", "theme": "Theme", "dark": "Dark", "light": "Light", "auto": "Auto", "accentColor": "Accent Color", "colorPicker": "Color Picker", "transparency": "Transparency", "links": "Links", "addLink": "Add Link", "removeLink": "Remove Link", "linkLabel": "Label", "linkUrl": "URL", "linkCategory": "Category", "linkFavorite": "Favorite", "weather": "Weather", "openWeatherApiKey": "OpenWeather API Key", "weatherLocation": "Location", "notifications": "Notifications", "notificationPosition": "Notification Position", "notificationDuration": "Notification Duration", "hours": "hours", "minutes": "minutes", "seconds": "seconds", "topRight": "Top Right", "topLeft": "Top Left", "bottomRight": "Bottom Right", "bottomLeft": "Bottom Left", "performance": "Performance", "reduceMotion": "Reduce Motion", "lowPowerMode": "Low Power Mode", "fps": "FPS", "memoryUsage": "Memory Usage", "cpuUsage": "CPU Usage", "security": "Security", "autoSave": "Auto Save", "backupInterval": "Backup Interval", "encryption": "Encryption", "privacy": "Privacy", "permissions": "Permissions", "developer": "Developer", "debugMode": "Debug Mode", "showPerformanceMetrics": "Show Performance Metrics", "console": "<PERSON><PERSON><PERSON>", "logs": "Logs", "version": "Version", "build": "Build", "resetToDefaults": "Reset to Defaults", "export": "Export", "import": "Import", "exportSettings": "Export Settings", "importSettings": "Import Settings", "createBackup": "Create Backup", "restoreFromBackup": "Restore from Backup", "confirmReset": "Are you sure you want to reset all settings to default values?", "confirmImport": "Are you sure you want to import these settings? Current settings will be replaced.", "settingsExported": "Settings exported", "settingsImported": "Settings imported", "errorImport": "Error importing settings"}, "time": {"title": "Time", "showSeconds": "Show Seconds", "showDate": "Show Date", "timezone": "Timezone", "currentTime": "Current Time", "currentDate": "Current Date", "format": "Format", "12hour": "12-hour", "24hour": "24-hour", "am": "AM", "pm": "PM"}, "links": {"title": "Links", "addLink": "Add Link", "removeLink": "Remove Link", "favorite": "Favorite", "unfavorite": "Unfavorite", "visitCount": "Visits", "exportLinks": "Export Links", "addDefaultLinks": "Add Default Links", "noLinks": "No links", "linksCount": "links", "linkAdded": "Link added", "linkRemoved": "Link removed", "linkUpdated": "Link updated", "noLabel": "No label", "category": "Category", "url": "URL", "label": "Label"}, "weather": {"title": "Weather", "temperature": "Temperature", "humidity": "<PERSON><PERSON><PERSON><PERSON>", "wind": "Wind", "pressure": "Pressure", "feelsLike": "Feels Like", "sunrise": "Sunrise", "sunset": "Sunset", "refresh": "Refresh", "noData": "No weather data", "apiKeyRequired": "API key required", "locationRequired": "Location required", "errorLoading": "Error loading data", "celsius": "°C", "fahrenheit": "°F", "kmh": "km/h", "mph": "mph", "hPa": "hPa", "mmHg": "mmHg"}, "notes": {"title": "Notes", "addNote": "Add Note", "editNote": "Edit", "saveNote": "Save", "cancelEdit": "Cancel", "deleteNote": "Delete Note", "clearAll": "Clear All", "confirmClear": "Are you sure you want to delete all notes?", "welcome": "Welcome to <PERSON><PERSON><PERSON>! This is your first note.", "loadingError": "Error loading notes", "notePlaceholder": "Enter note text...", "noteSaved": "Note saved", "noteDeleted": "Note deleted", "maxNotesReached": "Maximum number of notes reached"}, "widgets": {"title": "Widgets", "time": "Time", "links": "Links", "weather": "Weather", "notes": "Notes", "active": "active", "activeWidgets": "Active Widgets", "availableWidgets": "Available Widgets", "activate": "Activate", "deactivate": "Deactivate", "customize": "Customize", "resetToDefaults": "Reset to Defaults", "exportConfig": "Export Configuration", "confirmReset": "Are you sure you want to reset all widgets to default settings?", "dragToReorder": "Drag to reorder", "widgetSettings": "Widget <PERSON>s", "position": "Position", "size": "Size", "theme": "Theme", "enabled": "Enabled", "disabled": "Disabled", "on": "On", "off": "Off"}, "common": {"save": "Save", "cancel": "Cancel", "delete": "Delete", "edit": "Edit", "add": "Add", "remove": "Remove", "close": "Close", "apply": "Apply", "reset": "Reset", "export": "Export", "import": "Import", "loading": "Loading...", "error": "Error", "success": "Success", "warning": "Warning", "info": "Information", "yes": "Yes", "no": "No", "ok": "OK", "back": "Back", "next": "Next", "previous": "Previous", "search": "Search", "filter": "Filter", "sort": "Sort", "refresh": "Refresh", "settings": "Settings", "help": "Help", "about": "About", "menu": "<PERSON><PERSON>", "navigation": "Navigation", "breadcrumb": "Breadcrumb", "pagination": "Pagination", "milliseconds": "ms", "hours": "h"}}