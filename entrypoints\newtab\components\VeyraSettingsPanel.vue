<template>
	<div class="settings-panel">
		<!-- Заголовок -->
		<div class="settings-header">
			<h2 class="settings-title">Настройки Veyra</h2>
			<button class="close-btn" @click="$emit('close')">
				<X :size="20" />
			</button>
		</div>

		<!-- Навигация по разделам -->
		<div class="settings-nav">
			<button
				v-for="section in sections"
				:key="section.id"
				class="nav-item"
				:class="{ active: activeSection === section.id }"
				@click="activeSection = section.id"
			>
				<component :is="section.icon" :size="18" />
				<span>{{ section.name }}</span>
			</button>
		</div>

		<!-- Содержимое настроек -->
		<div class="settings-content">
			<!-- Плагины -->
			<div v-if="activeSection === 'plugins'" class="section">
				<h3>Плагины</h3>
				<div class="plugins-list">
					<div
						v-for="plugin in availablePlugins"
						:key="plugin.id"
						class="plugin-item"
						:class="{ active: plugin.active }"
					>
						<div class="plugin-info">
							<h4>{{ plugin.name }}</h4>
							<p>{{ plugin.description }}</p>
						</div>
						<div class="plugin-controls">
							<button
								class="toggle-btn"
								:class="{ active: plugin.active }"
								@click="togglePlugin(plugin.id)"
							>
								{{ plugin.active ? "Выключить" : "Включить" }}
							</button>
							<button
								v-if="plugin.active && plugin.settingsComponent"
								class="settings-btn"
								@click="openPluginSettings(plugin.id)"
							>
								<Settings :size="16" />
							</button>
						</div>
					</div>
				</div>
			</div>

			<!-- Внешний вид -->
			<div v-if="activeSection === 'appearance'" class="section">
				<h3>Внешний вид</h3>

				<!-- Тема -->
				<div class="setting-group">
					<label>Тема</label>
					<select v-model="appearance.theme" @change="updateAppearance">
						<option value="auto">Автоматически</option>
						<option value="light">Светлая</option>
						<option value="dark">Темная</option>
					</select>
				</div>

				<!-- Акцентный цвет -->
				<div class="setting-group">
					<label>Акцентный цвет</label>
					<div class="color-picker">
						<input
							type="color"
							v-model="appearance.accentColor"
							@change="updateAppearance"
						/>
						<span>{{ appearance.accentColor }}</span>
					</div>
				</div>

				<!-- Шрифт -->
				<div class="setting-group">
					<label>Шрифт интерфейса</label>
					<select v-model="appearance.fontFamily" @change="updateAppearance">
						<option value="Onest">Onest (рекомендуется)</option>
						<option value="system">Системный</option>
						<option value="Inter">Inter</option>
						<option value="Roboto">Roboto</option>
					</select>
				</div>

				<!-- Размер шрифта -->
				<div class="setting-group">
					<label>Размер шрифта</label>
					<input
						type="range"
						min="12"
						max="20"
						v-model="appearance.fontSize"
						@input="updateAppearance"
					/>
					<span>{{ appearance.fontSize }}px</span>
				</div>
			</div>

			<!-- Импорт/Экспорт -->
			<div v-if="activeSection === 'import-export'" class="section">
				<h3>Импорт и экспорт</h3>

				<!-- Экспорт -->
				<div class="setting-group">
					<h4>Экспорт настроек</h4>
					<p>Сохраните все настройки в файл .veyra.json</p>
					<button class="export-btn" @click="exportSettings">
						<Download :size="16" />
						Экспортировать настройки
					</button>
				</div>

				<!-- Импорт -->
				<div class="setting-group">
					<h4>Импорт настроек</h4>
					<p>Загрузите настройки из файла .veyra.json или tabliss.json</p>
					<input
						ref="importInput"
						type="file"
						accept=".json"
						style="display: none"
						@change="importSettings"
					/>
					<button class="import-btn" @click="$refs.importInput.click()">
						<Upload :size="16" />
						Импортировать настройки
					</button>
				</div>

				<!-- Сброс -->
				<div class="setting-group">
					<h4>Сброс настроек</h4>
					<p>Вернуть все настройки к значениям по умолчанию</p>
					<button class="reset-btn" @click="resetSettings">
						<RotateCcw :size="16" />
						Сбросить настройки
					</button>
				</div>
			</div>

			<!-- О программе -->
			<div v-if="activeSection === 'about'" class="section">
				<h3>О программе</h3>
				<div class="about-info">
					<div class="logo">
						<h2>Veyra</h2>
						<p>Версия 1.0.0</p>
					</div>
					<p>Минималистичная стартовая страница, вдохновлённая Tabliss</p>
					<div class="links">
						<a href="#" target="_blank">GitHub</a>
						<a href="#" target="_blank">Документация</a>
						<a href="#" target="_blank">Поддержка</a>
					</div>
				</div>
			</div>
		</div>
	</div>
</template>

<script setup>
import { ref, computed, onMounted } from "vue";
import {
	X,
	Settings,
	Palette,
	Download,
	Upload,
	RotateCcw,
	Puzzle,
	Info,
} from "lucide-vue-next";
import { pluginSystem } from "../utils/pluginSystem.js";

const emit = defineEmits(["close"]);

// Состояние
const activeSection = ref("plugins");
const selectedPlugin = ref(null);

// Настройки внешнего вида
const appearance = ref({
	theme: "auto",
	accentColor: "#8B45C7",
	fontFamily: "Onest",
	fontSize: 16,
});

// Разделы настроек
const sections = [
	{ id: "plugins", name: "Плагины", icon: Puzzle },
	{ id: "appearance", name: "Внешний вид", icon: Palette },
	{ id: "import-export", name: "Импорт/Экспорт", icon: Download },
	{ id: "about", name: "О программе", icon: Info },
];

// Доступные плагины
const availablePlugins = computed(() => {
	return pluginSystem.getPlugins().map((plugin) => ({
		...plugin,
		active: pluginSystem.getActivePlugins().some((p) => p.id === plugin.id),
	}));
});

// Методы
const togglePlugin = async (pluginId) => {
	const plugin = pluginSystem.getPlugin(pluginId);
	if (!plugin) return;

	if (plugin.active) {
		await pluginSystem.deactivatePlugin(pluginId);
	} else {
		await pluginSystem.activatePlugin(pluginId);
	}

	// Сохраняем список активных плагинов
	await pluginSystem.saveActivePlugins();
};

const openPluginSettings = (pluginId) => {
	selectedPlugin.value = pluginSystem.getPlugin(pluginId);
};

const updateAppearance = async () => {
	try {
		// Сохраняем настройки внешнего вида
		await chrome.storage.sync.set({ appearance: appearance.value });

		// Применяем изменения
		applyAppearanceSettings();
	} catch (error) {
		console.error("Failed to update appearance:", error);
	}
};

const applyAppearanceSettings = () => {
	const root = document.documentElement;

	// Применяем акцентный цвет
	root.style.setProperty("--accent-color", appearance.value.accentColor);

	// Применяем шрифт
	if (appearance.value.fontFamily !== "system") {
		root.style.setProperty("--font-family", appearance.value.fontFamily);
	}

	// Применяем размер шрифта
	root.style.setProperty("--font-size", `${appearance.value.fontSize}px`);

	// Применяем тему
	if (appearance.value.theme !== "auto") {
		root.setAttribute("data-theme", appearance.value.theme);
	} else {
		root.removeAttribute("data-theme");
	}
};

const exportSettings = async () => {
	try {
		// Собираем все настройки
		const settings = {
			version: "1.0.0",
			timestamp: new Date().toISOString(),
			appearance: appearance.value,
			plugins: {},
		};

		// Экспортируем настройки каждого плагина
		const activePlugins = pluginSystem.getActivePlugins();
		for (const plugin of activePlugins) {
			settings.plugins[plugin.id] = plugin.exportSettings();
		}

		// Создаем и скачиваем файл
		const blob = new Blob([JSON.stringify(settings, null, 2)], {
			type: "application/json",
		});
		const url = URL.createObjectURL(blob);
		const a = document.createElement("a");
		a.href = url;
		a.download = `veyra-settings-${
			new Date().toISOString().split("T")[0]
		}.json`;
		a.click();
		URL.revokeObjectURL(url);
	} catch (error) {
		console.error("Failed to export settings:", error);
		alert("Ошибка при экспорте настроек");
	}
};

const importSettings = async (event) => {
	const file = event.target.files[0];
	if (!file) return;

	try {
		const text = await file.text();
		const settings = JSON.parse(text);

		// Определяем тип файла
		if (settings.version && settings.plugins) {
			// Файл Veyra
			await importVeyraSettings(settings);
		} else if (settings.widgets || settings.background) {
			// Файл Tabliss
			await importTablissSettings(settings);
		} else {
			throw new Error("Неизвестный формат файла");
		}

		alert("Настройки успешно импортированы");
	} catch (error) {
		console.error("Failed to import settings:", error);
		alert("Ошибка при импорте настроек: " + error.message);
	}

	// Очищаем input
	event.target.value = "";
};

const importVeyraSettings = async (settings) => {
	// Импортируем настройки внешнего вида
	if (settings.appearance) {
		appearance.value = { ...appearance.value, ...settings.appearance };
		await updateAppearance();
	}

	// Импортируем настройки плагинов
	if (settings.plugins) {
		for (const [pluginId, pluginSettings] of Object.entries(settings.plugins)) {
			const plugin = pluginSystem.getPlugin(pluginId);
			if (plugin) {
				await plugin.importSettings(pluginSettings);
			}
		}
	}
};

const importTablissSettings = async (settings) => {
	// TODO: Реализовать импорт из Tabliss
	console.log("Importing Tabliss settings:", settings);
};

const resetSettings = async () => {
	if (!confirm("Вы уверены, что хотите сбросить все настройки?")) {
		return;
	}

	try {
		// Сбрасываем настройки всех плагинов
		const activePlugins = pluginSystem.getActivePlugins();
		for (const plugin of activePlugins) {
			await plugin.resetSettings();
		}

		// Сбрасываем настройки внешнего вида
		appearance.value = {
			theme: "auto",
			accentColor: "#8B45C7",
			fontFamily: "Onest",
			fontSize: 16,
		};
		await updateAppearance();

		alert("Настройки сброшены");
	} catch (error) {
		console.error("Failed to reset settings:", error);
		alert("Ошибка при сбросе настроек");
	}
};

// Загружаем настройки при монтировании
onMounted(async () => {
	try {
		const result = await chrome.storage.sync.get("appearance");
		if (result.appearance) {
			appearance.value = { ...appearance.value, ...result.appearance };
			applyAppearanceSettings();
		}
	} catch (error) {
		console.error("Failed to load appearance settings:", error);
	}
});
</script>

<style scoped>
.settings-panel {
	width: 100%;
	height: 100%;
	display: flex;
	flex-direction: column;
	background: rgba(255, 255, 255, 0.95);
	backdrop-filter: blur(20px);
	color: #333;
}

.settings-header {
	display: flex;
	align-items: center;
	justify-content: space-between;
	padding: 20px;
	border-bottom: 1px solid rgba(0, 0, 0, 0.1);
}

.settings-title {
	margin: 0;
	font-size: 1.5rem;
	font-weight: 600;
	color: var(--accent-color, #8b45c7);
}

.close-btn {
	background: none;
	border: none;
	cursor: pointer;
	padding: 8px;
	border-radius: 8px;
	transition: background-color 0.2s ease;
}

.close-btn:hover {
	background: rgba(0, 0, 0, 0.1);
}

.settings-nav {
	display: flex;
	flex-direction: column;
	padding: 20px 0;
	border-bottom: 1px solid rgba(0, 0, 0, 0.1);
}

.nav-item {
	display: flex;
	align-items: center;
	padding: 12px 20px;
	background: none;
	border: none;
	cursor: pointer;
	text-align: left;
	transition: background-color 0.2s ease;
}

.nav-item:hover {
	background: rgba(0, 0, 0, 0.05);
}

.nav-item.active {
	background: rgba(139, 69, 199, 0.1);
	color: var(--accent-color, #8b45c7);
}

.nav-item svg {
	margin-right: 12px;
}

.settings-content {
	flex: 1;
	padding: 20px;
	overflow-y: auto;
}

.section h3 {
	margin: 0 0 20px 0;
	font-size: 1.25rem;
	font-weight: 600;
}

.setting-group {
	margin-bottom: 24px;
}

.setting-group label {
	display: block;
	margin-bottom: 8px;
	font-weight: 500;
}

.setting-group select,
.setting-group input[type="range"] {
	width: 100%;
	padding: 8px 12px;
	border: 1px solid rgba(0, 0, 0, 0.2);
	border-radius: 8px;
	background: white;
}

.color-picker {
	display: flex;
	align-items: center;
	gap: 12px;
}

.color-picker input[type="color"] {
	width: 40px;
	height: 40px;
	border: none;
	border-radius: 8px;
	cursor: pointer;
}

.plugins-list {
	display: flex;
	flex-direction: column;
	gap: 12px;
}

.plugin-item {
	display: flex;
	align-items: center;
	justify-content: space-between;
	padding: 16px;
	background: rgba(0, 0, 0, 0.05);
	border-radius: 12px;
	transition: background-color 0.2s ease;
}

.plugin-item.active {
	background: rgba(139, 69, 199, 0.1);
}

.plugin-info h4 {
	margin: 0 0 4px 0;
	font-weight: 600;
}

.plugin-info p {
	margin: 0;
	color: rgba(0, 0, 0, 0.6);
	font-size: 0.9rem;
}

.plugin-controls {
	display: flex;
	gap: 8px;
}

.toggle-btn,
.settings-btn,
.export-btn,
.import-btn,
.reset-btn {
	padding: 8px 16px;
	border: 1px solid rgba(0, 0, 0, 0.2);
	border-radius: 8px;
	background: white;
	cursor: pointer;
	transition: all 0.2s ease;
	display: flex;
	align-items: center;
	gap: 8px;
}

.toggle-btn.active {
	background: var(--accent-color, #8b45c7);
	color: white;
	border-color: var(--accent-color, #8b45c7);
}

.export-btn,
.import-btn {
	background: var(--accent-color, #8b45c7);
	color: white;
	border-color: var(--accent-color, #8b45c7);
}

.reset-btn {
	background: #dc3545;
	color: white;
	border-color: #dc3545;
}

.about-info {
	text-align: center;
}

.about-info .logo h2 {
	margin: 0;
	font-size: 2rem;
	color: var(--accent-color, #8b45c7);
}

.about-info .links {
	margin-top: 20px;
	display: flex;
	justify-content: center;
	gap: 20px;
}

.about-info .links a {
	color: var(--accent-color, #8b45c7);
	text-decoration: none;
}

.about-info .links a:hover {
	text-decoration: underline;
}

/* Темная тема */
@media (prefers-color-scheme: dark) {
	.settings-panel {
		background: rgba(30, 30, 30, 0.95);
		color: white;
	}

	.settings-header {
		border-bottom-color: rgba(255, 255, 255, 0.1);
	}

	.close-btn:hover {
		background: rgba(255, 255, 255, 0.1);
	}

	.nav-item:hover {
		background: rgba(255, 255, 255, 0.05);
	}

	.plugin-item {
		background: rgba(255, 255, 255, 0.05);
	}

	.plugin-info p {
		color: rgba(255, 255, 255, 0.6);
	}

	.setting-group select,
	.toggle-btn,
	.settings-btn {
		background: rgba(255, 255, 255, 0.1);
		border-color: rgba(255, 255, 255, 0.2);
		color: white;
	}
}
</style>
