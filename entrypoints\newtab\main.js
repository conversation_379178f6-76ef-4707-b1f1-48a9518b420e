import { createApp } from "vue";
import { createPinia } from "pinia";
import App from "./App.vue";
import { i18n } from "./i18n";

// Импорт глобальных стилей
import "./styles/global.css";

// Импорт основных иконок Lucide
import {
	Settings,
	Clock,
	Link,
	Cloud,
	FileText,
	Plus,
	X,
	ChevronRight,
	Download,
	Upload,
	Globe,
	Sun,
	Moon,
	Palette,
	Layout,
	Grid,
	List,
	Eye,
	EyeOff,
	Move,
	GripVertical,
	Search,
	Filter,
	Star,
	Heart,
	Zap,
	Target,
	TrendingUp,
	BarChart3,
	Calendar,
	MapPin,
	Thermometer,
	Wind,
	Droplets,
	CloudRain,
	CloudLightning,
	Snowflake,
	Sunrise,
	Sunset,
} from "lucide-vue-next";

const app = createApp(App);
const pinia = createPinia();

// Глобальная регистрация иконок
app.component("IconSettings", Settings);
app.component("IconClock", Clock);
app.component("IconLink", Link);
app.component("Cloud", Cloud);
app.component("IconFileText", FileText);
app.component("IconPlus", Plus);
app.component("IconX", X);
app.component("IconChevronRight", ChevronRight);
app.component("IconDownload", Download);
app.component("IconUpload", Upload);
app.component("IconGlobe", Globe);
app.component("IconSun", Sun);
app.component("IconMoon", Moon);
app.component("IconPalette", Palette);
app.component("IconLayout", Layout);
app.component("IconGrid", Grid);
app.component("IconList", List);
app.component("IconEye", Eye);
app.component("IconEyeOff", EyeOff);
app.component("IconMove", Move);
app.component("IconGripVertical", GripVertical);
app.component("IconSearch", Search);
app.component("IconFilter", Filter);
app.component("IconStar", Star);
app.component("IconHeart", Heart);
app.component("IconZap", Zap);
app.component("IconTarget", Target);
app.component("IconTrendingUp", TrendingUp);
app.component("IconBarChart3", BarChart3);
app.component("IconCalendar", Calendar);
app.component("IconMapPin", MapPin);
app.component("IconThermometer", Thermometer);
app.component("IconWind", Wind);
app.component("IconDroplets", Droplets);
app.component("IconCloudRain", CloudRain);
app.component("IconCloudLightning", CloudLightning);
app.component("IconSnowflake", Snowflake);
app.component("IconSunrise", Sunrise);
app.component("IconSunset", Sunset);

app.use(pinia);
app.use(i18n);
app.mount("#app");
