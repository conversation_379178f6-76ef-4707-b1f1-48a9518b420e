/**
 * Система импорта и экспорта настроек Veyra
 * Поддерживает .veyra.json, tabliss.json и частичный импорт
 */

import { pluginSystem } from './pluginSystem.js';

/**
 * Экспортирует все настройки в формате Veyra
 */
export async function exportVeyraSettings() {
  try {
    // Получаем настройки внешнего вида
    const appearanceResult = await chrome.storage.sync.get('appearance');
    const appearance = appearanceResult.appearance || {};

    // Собираем настройки всех плагинов
    const plugins = {};
    const activePlugins = pluginSystem.getActivePlugins();
    
    for (const plugin of activePlugins) {
      plugins[plugin.id] = plugin.exportSettings();
    }

    // Формируем полный экспорт
    const exportData = {
      version: '1.0.0',
      format: 'veyra',
      timestamp: new Date().toISOString(),
      metadata: {
        exportedBy: 'Veyra Browser Extension',
        userAgent: navigator.userAgent,
        activePlugins: activePlugins.map(p => p.id)
      },
      appearance,
      plugins
    };

    return exportData;
  } catch (error) {
    console.error('Failed to export Veyra settings:', error);
    throw error;
  }
}

/**
 * Импортирует настройки в формате Veyra
 */
export async function importVeyraSettings(data, options = {}) {
  try {
    const { 
      importAppearance = true, 
      importPlugins = true,
      overwriteExisting = true 
    } = options;

    // Валидируем данные
    if (!validateVeyraFormat(data)) {
      throw new Error('Неверный формат файла Veyra');
    }

    const results = {
      appearance: false,
      plugins: {},
      errors: []
    };

    // Импортируем настройки внешнего вида
    if (importAppearance && data.appearance) {
      try {
        await chrome.storage.sync.set({ appearance: data.appearance });
        results.appearance = true;
      } catch (error) {
        results.errors.push(`Ошибка импорта внешнего вида: ${error.message}`);
      }
    }

    // Импортируем настройки плагинов
    if (importPlugins && data.plugins) {
      for (const [pluginId, pluginData] of Object.entries(data.plugins)) {
        try {
          const plugin = pluginSystem.getPlugin(pluginId);
          if (plugin) {
            const success = await plugin.importSettings(pluginData);
            results.plugins[pluginId] = success;
            
            // Активируем плагин если он не активен
            if (success && !plugin.active) {
              await pluginSystem.activatePlugin(pluginId);
            }
          } else {
            results.errors.push(`Плагин ${pluginId} не найден`);
          }
        } catch (error) {
          results.errors.push(`Ошибка импорта плагина ${pluginId}: ${error.message}`);
        }
      }
    }

    // Сохраняем список активных плагинов
    await pluginSystem.saveActivePlugins();

    return results;
  } catch (error) {
    console.error('Failed to import Veyra settings:', error);
    throw error;
  }
}

/**
 * Импортирует настройки из Tabliss
 */
export async function importTablissSettings(data, options = {}) {
  try {
    const { 
      importBackground = true,
      importLinks = true,
      importWidgets = true 
    } = options;

    // Валидируем данные Tabliss
    if (!validateTablissFormat(data)) {
      throw new Error('Неверный формат файла Tabliss');
    }

    const results = {
      background: false,
      links: false,
      widgets: {},
      errors: []
    };

    // Конвертируем фон
    if (importBackground && data.background) {
      try {
        const backgroundSettings = convertTablissBackground(data.background);
        const backgroundPlugin = pluginSystem.getPlugin('background');
        
        if (backgroundPlugin) {
          await backgroundPlugin.updateSettings(backgroundSettings);
          await pluginSystem.activatePlugin('background');
          results.background = true;
        }
      } catch (error) {
        results.errors.push(`Ошибка импорта фона: ${error.message}`);
      }
    }

    // Конвертируем ссылки
    if (importLinks && data.links) {
      try {
        const linksSettings = convertTablissLinks(data.links);
        const linksPlugin = pluginSystem.getPlugin('links');
        
        if (linksPlugin) {
          await linksPlugin.updateSettings(linksSettings);
          await pluginSystem.activatePlugin('links');
          results.links = true;
        }
      } catch (error) {
        results.errors.push(`Ошибка импорта ссылок: ${error.message}`);
      }
    }

    // Конвертируем виджеты
    if (importWidgets && data.widgets) {
      for (const widget of data.widgets) {
        try {
          const convertedWidget = convertTablissWidget(widget);
          if (convertedWidget) {
            const plugin = pluginSystem.getPlugin(convertedWidget.pluginId);
            if (plugin) {
              await plugin.updateSettings(convertedWidget.settings);
              await pluginSystem.activatePlugin(convertedWidget.pluginId);
              results.widgets[convertedWidget.pluginId] = true;
            }
          }
        } catch (error) {
          results.errors.push(`Ошибка импорта виджета: ${error.message}`);
        }
      }
    }

    await pluginSystem.saveActivePlugins();
    return results;
  } catch (error) {
    console.error('Failed to import Tabliss settings:', error);
    throw error;
  }
}

/**
 * Валидирует формат Veyra
 */
function validateVeyraFormat(data) {
  return (
    data &&
    typeof data === 'object' &&
    data.version &&
    (data.format === 'veyra' || data.plugins || data.appearance)
  );
}

/**
 * Валидирует формат Tabliss
 */
function validateTablissFormat(data) {
  return (
    data &&
    typeof data === 'object' &&
    (data.background || data.widgets || data.links)
  );
}

/**
 * Конвертирует фон из Tabliss в Veyra
 */
function convertTablissBackground(tablissBackground) {
  const veyraBackground = {
    type: 'solid',
    solidColor: '#8B45C7'
  };

  if (tablissBackground.type === 'colour') {
    veyraBackground.type = 'solid';
    veyraBackground.solidColor = tablissBackground.colour || '#8B45C7';
  } else if (tablissBackground.type === 'gradient') {
    veyraBackground.type = 'gradient';
    veyraBackground.gradientType = 'linear';
    veyraBackground.gradientDirection = `${tablissBackground.angle || 45}deg`;
    veyraBackground.gradientColors = (tablissBackground.colours || ['#8B45C7', '#6366F1']).map((color, index) => ({
      color,
      position: (index / (tablissBackground.colours.length - 1)) * 100
    }));
  } else if (tablissBackground.type === 'image' || tablissBackground.type === 'unsplash') {
    veyraBackground.type = tablissBackground.type === 'unsplash' ? 'unsplash' : 'image';
    if (tablissBackground.url) {
      veyraBackground.imageUrl = tablissBackground.url;
    }
    if (tablissBackground.query) {
      veyraBackground.unsplashQuery = tablissBackground.query;
    }
  }

  return veyraBackground;
}

/**
 * Конвертирует ссылки из Tabliss в Veyra
 */
function convertTablissLinks(tablissLinks) {
  const veyraLinks = {
    links: [],
    folders: [],
    displayMode: 'grid',
    showLabels: true,
    showFavicons: true
  };

  if (Array.isArray(tablissLinks)) {
    veyraLinks.links = tablissLinks.map((link, index) => ({
      id: `imported_${index}`,
      title: link.label || link.title || 'Ссылка',
      url: link.url || '#',
      icon: {
        type: 'favicon',
        url: `https://${new URL(link.url).hostname}/favicon.ico`
      },
      folder: null,
      order: index
    }));
  }

  return veyraLinks;
}

/**
 * Конвертирует виджет из Tabliss в Veyra
 */
function convertTablissWidget(tablissWidget) {
  switch (tablissWidget.type) {
    case 'time':
    case 'clock':
      return {
        pluginId: 'time',
        settings: {
          format: tablissWidget.format === '12h' ? '12h' : '24h',
          showSeconds: tablissWidget.showSeconds || false,
          showDate: tablissWidget.showDate !== false,
          position: 'center',
          alignment: 'center'
        }
      };

    case 'links':
      return {
        pluginId: 'links',
        settings: convertTablissLinks(tablissWidget.links || [])
      };

    default:
      return null;
  }
}

/**
 * Создает файл для скачивания
 */
export function downloadFile(data, filename, type = 'application/json') {
  const blob = new Blob([JSON.stringify(data, null, 2)], { type });
  const url = URL.createObjectURL(blob);
  const a = document.createElement('a');
  a.href = url;
  a.download = filename;
  a.click();
  URL.revokeObjectURL(url);
}

/**
 * Читает файл как JSON
 */
export function readFileAsJSON(file) {
  return new Promise((resolve, reject) => {
    const reader = new FileReader();
    reader.onload = (e) => {
      try {
        const data = JSON.parse(e.target.result);
        resolve(data);
      } catch (error) {
        reject(new Error('Не удалось прочитать файл как JSON'));
      }
    };
    reader.onerror = () => reject(new Error('Ошибка чтения файла'));
    reader.readAsText(file);
  });
}

/**
 * Определяет тип файла настроек
 */
export function detectSettingsFormat(data) {
  if (validateVeyraFormat(data)) {
    return 'veyra';
  } else if (validateTablissFormat(data)) {
    return 'tabliss';
  } else {
    return 'unknown';
  }
}

/**
 * Создает резервную копию текущих настроек
 */
export async function createBackup() {
  try {
    const settings = await exportVeyraSettings();
    const filename = `veyra-backup-${new Date().toISOString().split('T')[0]}.json`;
    downloadFile(settings, filename);
    return { success: true, filename };
  } catch (error) {
    console.error('Failed to create backup:', error);
    return { success: false, error: error.message };
  }
}
