import { defineStore } from "pinia";
import { ref, computed } from "vue";

export const useSettingsStore = defineStore("settings", () => {
	// Состояние
	const settings = ref({
		// Основные настройки
		showTime: true,
		showLinks: true,
		language: "ru",

		// Настройки фона
		backgroundType: "gradient", // solid | gradient | image
		solidColor: "#1a1a1a",
		gradient: {
			type: "linear",
			angle: 45,
			colors: ["#667eea", "#764ba2"],
			animation: true,
			animationSpeed: 20,
			animationTiming: "linear",
			posX: 50,
			posY: 50,
		},
		backgroundImage: "",
		backgroundBlur: 0,
		backgroundOpacity: 1,

		// Настройки ссылок
		links: [{ label: "GitHub", url: "https://github.com" }],

		// Настройки погоды
		openWeatherApiKey: "",
		weatherLocation: "Moscow",

		// Настройки виджетов
		activeWidgets: [
			{ id: "time", active: true, component: "TimeDisplay" },
			{ id: "links", active: true, component: "LinksDisplay" },
		],

		// Настройки темы
		theme: "dark", // dark | light | auto
		accentColor: "#667eea",
		borderRadius: "medium", // small | medium | large
		shadows: true,
		animations: true,

		// Настройки производительности
		reduceMotion: false,
		lowPowerMode: false,

		// Настройки уведомлений
		notifications: true,
		notificationPosition: "top-right",
		notificationDuration: 3000,

		// Настройки безопасности
		autoSave: true,
		backupInterval: 24, // часы
		lastBackup: null,

		// Настройки разработчика
		debugMode: false,
		showPerformanceMetrics: false,
	});

	// Геттеры
	const isTimeVisible = computed(() => settings.value.showTime);
	const isLinksVisible = computed(() => settings.value.showLinks);
	const currentLanguage = computed(() => settings.value.language);
	const backgroundConfig = computed(() =>
		settings.value.backgroundType === "gradient"
			? settings.value.gradient
			: null
	);
	const activeWidgetsList = computed(() =>
		settings.value.activeWidgets.filter((w) => w.active)
	);
	const currentTheme = computed(() => settings.value.theme);
	const isDarkMode = computed(() => {
		if (settings.value.theme === "auto") {
			return window.matchMedia("(prefers-color-scheme: dark)").matches;
		}
		return settings.value.theme === "dark";
	});

	// Действия
	const updateSetting = (key, value) => {
		if (key.includes(".")) {
			const keys = key.split(".");
			let current = settings.value;
			for (let i = 0; i < keys.length - 1; i++) {
				current = current[keys[i]];
			}
			current[keys[keys.length - 1]] = value;
		} else {
			settings.value[key] = value;
		}
		saveToStorage();
	};

	const addLink = () => {
		settings.value.links.push({ label: "", url: "" });
		saveToStorage();
	};

	const removeLink = (index) => {
		settings.value.links.splice(index, 1);
		saveToStorage();
	};

	const updateLink = (index, field, value) => {
		settings.value.links[index][field] = value;
		saveToStorage();
	};

	const addGradientColor = () => {
		settings.value.gradient.colors.push("#ffffff");
		saveToStorage();
	};

	const removeGradientColor = (index) => {
		if (settings.value.gradient.colors.length > 2) {
			settings.value.gradient.colors.splice(index, 1);
			saveToStorage();
		}
	};

	const toggleWidget = (widgetId) => {
		const widget = settings.value.activeWidgets.find((w) => w.id === widgetId);
		if (widget) {
			widget.active = !widget.active;
			saveToStorage();
		}
	};

	const updateWidgetOrder = (newOrder) => {
		settings.value.activeWidgets = newOrder;
		saveToStorage();
	};

	const updateActiveWidgets = (newWidgets) => {
		settings.value.activeWidgets = newWidgets;
		saveToStorage();
	};

	const updateLinks = (newLinks) => {
		settings.value.links = newLinks;
		saveToStorage();
	};

	const setLanguage = (lang) => {
		settings.value.language = lang;
		localStorage.setItem("veyra-lang", lang);
		saveToStorage();
	};

	const setTheme = (theme) => {
		settings.value.theme = theme;
		applyTheme(theme);
		saveToStorage();
	};

	const applyTheme = (theme) => {
		const root = document.documentElement;

		if (theme === "auto") {
			const isDark = window.matchMedia("(prefers-color-scheme: dark)").matches;
			root.setAttribute("data-theme", isDark ? "dark" : "light");
		} else {
			root.setAttribute("data-theme", theme);
		}
	};

	const toggleTheme = () => {
		const newTheme = settings.value.theme === "dark" ? "light" : "dark";
		setTheme(newTheme);
	};

	const exportSettings = () => {
		const data = JSON.stringify(settings.value, null, 2);
		const blob = new Blob([data], { type: "application/json" });
		const url = URL.createObjectURL(blob);

		const a = document.createElement("a");
		a.href = url;
		a.download = `veyra-settings-${new Date().toISOString().slice(0, 10)}.json`;
		document.body.appendChild(a);
		a.click();
		document.body.removeChild(a);
		URL.revokeObjectURL(url);
	};

	const importSettings = async (file) => {
		try {
			const text = await file.text();
			const newSettings = JSON.parse(text);

			// Валидация импортируемых настроек
			if (validateSettings(newSettings)) {
				Object.assign(settings.value, newSettings);
				applyTheme(settings.value.theme);
				saveToStorage();
				return { success: true };
			} else {
				return { success: false, error: "Неверный формат файла настроек" };
			}
		} catch (error) {
			return { success: false, error: "Ошибка чтения файла" };
		}
	};

	const validateSettings = (settingsToValidate) => {
		const requiredKeys = [
			"showTime",
			"showLinks",
			"backgroundType",
			"solidColor",
			"gradient",
			"links",
		];
		return requiredKeys.every((key) => key in settingsToValidate);
	};

	const resetToDefaults = () => {
		const defaultSettings = {
			showTime: true,
			showLinks: true,
			language: "ru",
			backgroundType: "gradient",
			solidColor: "#1a1a1a",
			gradient: {
				type: "linear",
				angle: 45,
				colors: ["#667eea", "#764ba2"],
				animation: true,
				animationSpeed: 20,
				animationTiming: "linear",
				posX: 50,
				posY: 50,
			},
			backgroundImage: "",
			backgroundBlur: 0,
			backgroundOpacity: 1,
			links: [{ label: "GitHub", url: "https://github.com" }],
			openWeatherApiKey: "",
			weatherLocation: "Moscow",
			activeWidgets: [
				{ id: "time", active: true, component: "TimeDisplay" },
				{ id: "links", active: true, component: "LinksDisplay" },
			],
			theme: "dark",
			accentColor: "#667eea",
			borderRadius: "medium",
			shadows: true,
			animations: true,
			reduceMotion: false,
			lowPowerMode: false,
			notifications: true,
			notificationPosition: "top-right",
			notificationDuration: 3000,
			autoSave: true,
			backupInterval: 24,
			lastBackup: null,
			debugMode: false,
			showPerformanceMetrics: false,
		};

		Object.assign(settings.value, defaultSettings);
		applyTheme(settings.value.theme);
		saveToStorage();
	};

	const resetWidgetsToDefaults = () => {
		// Сброс только виджетов к настройкам по умолчанию
		settings.value.activeWidgets = [
			{ id: "time", active: true, component: "TimeDisplay" },
			{ id: "links", active: true, component: "LinksDisplay" },
		];
		saveToStorage();
	};

	const saveToStorage = () => {
		try {
			localStorage.setItem("veyra-settings", JSON.stringify(settings.value));
		} catch (error) {
			console.error("Ошибка сохранения настроек:", error);
		}
	};

	const loadFromStorage = () => {
		try {
			const stored = localStorage.getItem("veyra-settings");
			if (stored) {
				const parsed = JSON.parse(stored);
				if (validateSettings(parsed)) {
					Object.assign(settings.value, parsed);
					applyTheme(settings.value.theme);
				}
			}
		} catch (error) {
			console.error("Ошибка загрузки настроек:", error);
		}
	};

	const createBackup = () => {
		const backup = {
			settings: settings.value,
			timestamp: new Date().toISOString(),
			version: "1.0.0",
		};

		try {
			localStorage.setItem("veyra-backup", JSON.stringify(backup));
			settings.value.lastBackup = new Date().toISOString();
			saveToStorage();
			return { success: true };
		} catch (error) {
			return { success: false, error: "Ошибка создания резервной копии" };
		}
	};

	const restoreFromBackup = () => {
		try {
			const backup = localStorage.getItem("veyra-backup");
			if (backup) {
				const parsed = JSON.parse(backup);
				if (parsed.settings && validateSettings(parsed.settings)) {
					Object.assign(settings.value, parsed.settings);
					applyTheme(settings.value.theme);
					saveToStorage();
					return { success: true };
				}
			}
			return {
				success: false,
				error: "Резервная копия не найдена или повреждена",
			};
		} catch (error) {
			return {
				success: false,
				error: "Ошибка восстановления из резервной копии",
			};
		}
	};

	// Инициализация
	loadFromStorage();

	// Применяем тему при загрузке
	applyTheme(settings.value.theme);

	// Слушаем изменения системной темы
	if (typeof window !== "undefined") {
		const mediaQuery = window.matchMedia("(prefers-color-scheme: dark)");
		mediaQuery.addEventListener("change", (e) => {
			if (settings.value.theme === "auto") {
				applyTheme("auto");
			}
		});
	}

	return {
		// Состояние
		settings,

		// Геттеры
		isTimeVisible,
		isLinksVisible,
		currentLanguage,
		backgroundConfig,
		activeWidgetsList,
		currentTheme,
		isDarkMode,

		// Действия
		updateSetting,
		addLink,
		removeLink,
		updateLink,
		addGradientColor,
		removeGradientColor,
		toggleWidget,
		updateWidgetOrder,
		updateActiveWidgets,
		updateLinks,
		setLanguage,
		setTheme,
		toggleTheme,
		exportSettings,
		importSettings,
		resetToDefaults,
		resetWidgetsToDefaults,
		saveToStorage,
		loadFromStorage,
		createBackup,
		restoreFromBackup,
	};
});
