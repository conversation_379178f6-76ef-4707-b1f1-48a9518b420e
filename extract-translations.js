import fs from "fs";
import path from "path";
import { fileURLToPath } from "url";

// Эмуляция __dirname в ESM
const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

const localesDir = path.resolve(__dirname, "entrypoints/newtab/locales");
const srcDir = path.resolve(__dirname, "entrypoints/newtab/components");

const extractKeysFromFiles = (dir) => {
	const keys = new Set();

	const walk = (folder) => {
		const entries = fs.readdirSync(folder, { withFileTypes: true });
		for (const entry of entries) {
			const fullPath = path.join(folder, entry.name);
			if (entry.isDirectory()) {
				walk(fullPath);
			} else if (entry.name.endsWith(".vue") || entry.name.endsWith(".js")) {
				const content = fs.readFileSync(fullPath, "utf-8");
				const matches = content.matchAll(/(?:\$| )t\(["'`](.+?)["'`]\)/g);
				for (const match of matches) {
					keys.add(match[1]);
				}
			}
		}
	};

	walk(dir);
	return keys;
};

const updateLocale = (localePath, keys) => {
	let current = {};
	if (fs.existsSync(localePath)) {
		current = JSON.parse(fs.readFileSync(localePath, "utf-8"));
	}

	let updated = false;
	for (const key of keys) {
		if (!current[key]) {
			current[key] = "";
			updated = true;
		}
	}

	if (updated) {
		fs.writeFileSync(localePath, JSON.stringify(current, null, 2));
		console.log(`✅ Обновлён: ${path.basename(localePath)}`);
	} else {
		console.log(`👌 Всё актуально: ${path.basename(localePath)}`);
	}
};

const run = () => {
	const keys = extractKeysFromFiles(srcDir);
	const files = fs.readdirSync(localesDir).filter((f) => f.endsWith(".json"));

	for (const file of files) {
		const localePath = path.join(localesDir, file);
		updateLocale(localePath, keys);
	}
};

run();
