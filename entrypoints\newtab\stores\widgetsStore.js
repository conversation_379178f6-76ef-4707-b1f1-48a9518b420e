import { defineStore } from "pinia";
import { ref, computed } from "vue";

export const useWidgetsStore = defineStore("widgets", () => {
	// Состояние
	const widgets = ref([
		{
			id: "time",
			label: "Время и дата",
			component: "TimeDisplay",
			icon: "Clock",
			description: "Отображение текущего времени и даты",
			category: "essential",
			customizable: true,
			defaultSettings: {
				showSeconds: true,
				showDate: true,
				showTimezone: true,
				timeFormat: "24h",
				dateFormat: "long",
				position: "center",
				size: "large",
				theme: "default",
			},
		},
		{
			id: "links",
			label: "Быстрые ссылки",
			component: "LinksDisplay",
			icon: "Link",
			description: "Быстрый доступ к часто используемым сайтам",
			category: "essential",
			customizable: true,
			defaultSettings: {
				layout: "horizontal",
				showIcons: true,
				showLabels: true,
				maxLinks: 10,
				position: "bottom",
				size: "medium",
				theme: "default",
			},
		},
		{
			id: "weather",
			label: "Погода",
			component: "WeatherWidget",
			icon: "Cloud",
			description: "Информация о погоде в выбранном городе",
			category: "information",
			customizable: true,
			defaultSettings: {
				showDetails: true,
				showForecast: false,
				units: "metric",
				position: "top-right",
				size: "medium",
				theme: "default",
				refreshInterval: 30,
			},
		},
		{
			id: "notes",
			label: "Заметки",
			component: "NotesWidget",
			icon: "FileText",
			description: "Быстрые заметки для важной информации",
			category: "productivity",
			customizable: true,
			defaultSettings: {
				maxNotes: 5,
				noteHeight: "medium",
				showSearch: true,
				showStats: true,
				position: "left",
				size: "medium",
				theme: "default",
			},
		},
		{
			id: "calendar",
			label: "Календарь",
			component: "CalendarWidget",
			icon: "IconCalendar",
			description: "Просмотр событий и планирование",
			category: "productivity",
			customizable: true,
			defaultSettings: {
				view: "month",
				showEvents: true,
				showHolidays: true,
				position: "right",
				size: "large",
				theme: "default",
			},
		},
		{
			id: "stats",
			label: "Статистика",
			component: "StatsWidget",
			icon: "IconBarChart3",
			description: "Персональная статистика и аналитика",
			category: "productivity",
			customizable: true,
			defaultSettings: {
				showCharts: true,
				dataSource: "local",
				refreshInterval: 60,
				position: "bottom-right",
				size: "medium",
				theme: "default",
			},
		},
	]);

	const widgetInstances = ref(new Map());
	const widgetSettings = ref(new Map());

	// Геттеры
	const availableWidgets = computed(() => widgets.value);

	const getWidgetById = computed(
		() => (id) => widgets.value.find((w) => w.id === id)
	);

	const getWidgetsByCategory = computed(
		() => (category) => widgets.value.filter((w) => w.category === category)
	);

	const getActiveWidgets = computed(() => {
		const settingsStore = useSettingsStore();
		return settingsStore.activeWidgetsList;
	});

	const getWidgetSettings = computed(() => (widgetId) => {
		const widget = widgets.value.find((w) => w.id === widgetId);
		if (!widget) return null;

		return widgetSettings.value.get(widgetId) || { ...widget.defaultSettings };
	});

	// Действия
	const registerWidget = (id, instance) => {
		widgetInstances.value.set(id, instance);
	};

	const unregisterWidget = (id) => {
		widgetInstances.value.delete(id);
	};

	const getWidgetInstance = (id) => {
		return widgetInstances.value.get(id);
	};

	const addCustomWidget = (widgetConfig) => {
		const newWidget = {
			id: `custom-${Date.now()}`,
			...widgetConfig,
			category: "custom",
			customizable: true,
			defaultSettings: widgetConfig.defaultSettings || {},
		};
		widgets.value.push(newWidget);

		// Инициализируем настройки для нового виджета
		widgetSettings.value.set(newWidget.id, { ...newWidget.defaultSettings });
	};

	const removeCustomWidget = (id) => {
		const index = widgets.value.findIndex((w) => w.id === id);
		if (index !== -1 && widgets.value[index].category === "custom") {
			widgets.value.splice(index, 1);
			unregisterWidget(id);
			widgetSettings.value.delete(id);
		}
	};

	const updateWidgetConfig = (id, config) => {
		const widget = widgets.value.find((w) => w.id === id);
		if (widget) {
			Object.assign(widget, config);
		}
	};

	const updateWidgetSettings = (widgetId, settings) => {
		const currentSettings = widgetSettings.value.get(widgetId) || {};
		const widget = widgets.value.find((w) => w.id === widgetId);

		if (widget) {
			const newSettings = { ...currentSettings, ...settings };
			widgetSettings.value.set(widgetId, newSettings);

			// Сохраняем в localStorage
			try {
				localStorage.setItem(
					`veyra-widget-${widgetId}`,
					JSON.stringify(newSettings)
				);
			} catch (error) {
				console.error("Ошибка сохранения настроек виджета:", error);
			}
		}
	};

	const resetWidgetSettings = (widgetId) => {
		const widget = widgets.value.find((w) => w.id === widgetId);
		if (widget) {
			widgetSettings.value.set(widgetId, { ...widget.defaultSettings });

			// Удаляем из localStorage
			try {
				localStorage.removeItem(`veyra-widget-${widgetId}`);
			} catch (error) {
				console.error("Ошибка сброса настроек виджета:", error);
			}
		}
	};

	const loadWidgetSettings = () => {
		widgets.value.forEach((widget) => {
			try {
				const saved = localStorage.getItem(`veyra-widget-${widget.id}`);
				if (saved) {
					const parsed = JSON.parse(saved);
					widgetSettings.value.set(widget.id, {
						...widget.defaultSettings,
						...parsed,
					});
				} else {
					widgetSettings.value.set(widget.id, { ...widget.defaultSettings });
				}
			} catch (error) {
				console.error("Ошибка загрузки настроек виджета:", error);
				widgetSettings.value.set(widget.id, { ...widget.defaultSettings });
			}
		});
	};

	const exportWidgetSettings = () => {
		const exportData = {};
		widgetSettings.value.forEach((settings, widgetId) => {
			exportData[widgetId] = settings;
		});

		const data = JSON.stringify(exportData, null, 2);
		const blob = new Blob([data], { type: "application/json" });
		const url = URL.createObjectURL(blob);

		const a = document.createElement("a");
		a.href = url;
		a.download = `veyra-widgets-${new Date().toISOString().slice(0, 10)}.json`;
		document.body.appendChild(a);
		a.click();
		document.body.removeChild(a);
		URL.revokeObjectURL(url);
	};

	const importWidgetSettings = async (file) => {
		try {
			const text = await file.text();
			const importedSettings = JSON.parse(text);

			Object.keys(importedSettings).forEach((widgetId) => {
				const widget = widgets.value.find((w) => w.id === widgetId);
				if (widget) {
					const mergedSettings = {
						...widget.defaultSettings,
						...importedSettings[widgetId],
					};
					widgetSettings.value.set(widgetId, mergedSettings);

					// Сохраняем в localStorage
					try {
						localStorage.setItem(
							`veyra-widget-${widgetId}`,
							JSON.stringify(mergedSettings)
						);
					} catch (error) {
						console.error("Ошибка сохранения импортированных настроек:", error);
					}
				}
			});

			return { success: true };
		} catch (error) {
			return { success: false, error: "Ошибка импорта настроек виджетов" };
		}
	};

	// Инициализация
	loadWidgetSettings();

	return {
		// Состояние
		widgets,
		widgetInstances,
		widgetSettings,

		// Геттеры
		availableWidgets,
		getWidgetById,
		getWidgetsByCategory,
		getActiveWidgets,
		getWidgetSettings,

		// Действия
		registerWidget,
		unregisterWidget,
		getWidgetInstance,
		addCustomWidget,
		removeCustomWidget,
		updateWidgetConfig,
		updateWidgetSettings,
		resetWidgetSettings,
		exportWidgetSettings,
		importWidgetSettings,
	};
});
