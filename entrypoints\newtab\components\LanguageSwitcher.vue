<template>
	<div class="language-switcher">
		<button
			@click="toggleDropdown"
			class="language-toggle"
			:aria-expanded="isDropdownOpen"
			:aria-label="$t('language.switch')"
			:title="$t('language.current')"
		>
			<IconGlobe class="globe-icon" />
			<span class="current-language">{{ currentLanguageName }}</span>
			<IconChevronDown
				class="chevron-icon"
				:class="{ rotated: isDropdownOpen }"
			/>
		</button>

		<div
			v-if="isDropdownOpen"
			class="language-dropdown"
			:class="{ 'dropdown-open': isDropdownOpen }"
		>
			<div class="dropdown-header">
				<h4 class="dropdown-title">
					<IconGlobe class="dropdown-icon" />
					{{ $t("language.select") }}
				</h4>
				<button
					@click="closeDropdown"
					class="close-btn"
					:aria-label="$t('language.close')"
				>
					<IconX class="close-icon" />
				</button>
			</div>

			<div class="languages-list">
				<button
					v-for="lang in languages"
					:key="lang.code"
					@click="selectLanguage(lang.code)"
					class="language-option"
					:class="{
						active: lang.code === currentLanguage,
						recommended: lang.recommended,
					}"
					:aria-label="`${$t('language.select')} ${lang.name}`"
				>
					<div class="language-info">
						<span class="language-flag">{{ lang.flag }}</span>
						<div class="language-details">
							<span class="language-name">{{ lang.name }}</span>
							<span class="language-native">{{ lang.native }}</span>
						</div>
					</div>

					<div class="language-status">
						<span v-if="lang.recommended" class="recommended-badge">
							{{ $t("language.recommended") }}
						</span>
						<IconCheck
							v-if="lang.code === currentLanguage"
							class="check-icon"
						/>
					</div>
				</button>
			</div>

			<div class="dropdown-footer">
				<small class="language-tip">
					{{ $t("language.tip") }}
				</small>
			</div>
		</div>

		<!-- Backdrop для закрытия dropdown -->
		<div
			v-if="isDropdownOpen"
			class="dropdown-backdrop"
			@click="closeDropdown"
		></div>
	</div>
</template>

<script setup>
import { ref, computed, onMounted, onUnmounted } from "vue";
import { useSettingsStore } from "../stores/settingsStore";
import { Globe, ChevronDown, X, Check } from "lucide-vue-next";

const settingsStore = useSettingsStore();

// Реактивные данные
const isDropdownOpen = ref(false);

// Константы
const languages = [
	{
		code: "ru",
		name: "Русский",
		native: "Русский",
		flag: "🇷🇺",
		recommended: true,
	},
	{
		code: "en",
		name: "English",
		native: "English",
		flag: "🇺🇸",
		recommended: false,
	},
	{
		code: "de",
		name: "Deutsch",
		native: "Deutsch",
		flag: "🇩🇪",
		recommended: false,
	},
	{
		code: "fr",
		name: "Français",
		native: "Français",
		flag: "🇫🇷",
		recommended: false,
	},
	{
		code: "es",
		name: "Español",
		native: "Español",
		flag: "🇪🇸",
		recommended: false,
	},
	{
		code: "zh",
		name: "中文",
		native: "中文",
		flag: "🇨🇳",
		recommended: false,
	},
	{
		code: "ja",
		name: "日本語",
		native: "日本語",
		flag: "🇯🇵",
		recommended: false,
	},
];

// Вычисляемые свойства
const currentLanguage = computed(() => settingsStore.settings.language);

const currentLanguageName = computed(() => {
	const lang = languages.find((l) => l.code === currentLanguage.value);
	return lang ? lang.name : "Русский";
});

// Методы
const toggleDropdown = () => {
	isDropdownOpen.value = !isDropdownOpen.value;
};

const closeDropdown = () => {
	isDropdownOpen.value = false;
};

const selectLanguage = (languageCode) => {
	settingsStore.setLanguage(languageCode);
	closeDropdown();
};

// Обработчики событий
const handleClickOutside = (event) => {
	if (!event.target.closest(".language-switcher")) {
		closeDropdown();
	}
};

const handleEscape = (event) => {
	if (event.key === "Escape") {
		closeDropdown();
	}
};

// Жизненный цикл
onMounted(() => {
	document.addEventListener("click", handleClickOutside);
	document.addEventListener("keydown", handleEscape);
});

onUnmounted(() => {
	document.removeEventListener("click", handleClickOutside);
	document.removeEventListener("keydown", handleEscape);
});
</script>

<style scoped>
.language-switcher {
	position: relative;
	z-index: 1000;
}

.language-toggle {
	display: flex;
	align-items: center;
	gap: var(--spacing-sm);
	padding: var(--spacing-sm) var(--spacing-md);
	background: var(--bg-secondary);
	border: 1px solid var(--border-color);
	border-radius: var(--border-radius-md);
	color: var(--text-primary);
	cursor: pointer;
	font-size: 0.9em;
	font-weight: 500;
	transition: all var(--transition-normal);
	user-select: none;
}

.language-toggle:hover {
	background: var(--bg-tertiary);
	border-color: var(--border-hover);
	transform: translateY(-1px);
	box-shadow: 0 2px 8px var(--shadow-color);
}

.language-toggle:active {
	transform: translateY(0);
}

.globe-icon {
	width: 16px;
	height: 16px;
	color: var(--primary-color);
}

.current-language {
	font-weight: 600;
	min-width: 60px;
	text-align: center;
}

.chevron-icon {
	width: 14px;
	height: 14px;
	color: var(--text-secondary);
	transition: transform var(--transition-normal);
}

.chevron-icon.rotated {
	transform: rotate(180deg);
}

/* Dropdown */
.language-dropdown {
	position: absolute;
	top: calc(100% + var(--spacing-sm));
	right: 0;
	min-width: 280px;
	background: var(--bg-primary);
	border: 1px solid var(--border-color);
	border-radius: var(--border-radius-lg);
	box-shadow: 0 10px 25px var(--shadow-elevated);
	opacity: 0;
	transform: translateY(-10px);
	transition: all var(--transition-normal);
	z-index: 1001;
	overflow: hidden;
}

.language-dropdown.dropdown-open {
	opacity: 1;
	transform: translateY(0);
}

.dropdown-header {
	display: flex;
	align-items: center;
	justify-content: space-between;
	padding: var(--spacing-md) var(--spacing-lg);
	border-bottom: 1px solid var(--border-color);
	background: var(--bg-secondary);
}

.dropdown-title {
	display: flex;
	align-items: center;
	gap: var(--spacing-sm);
	margin: 0;
	font-size: 1em;
	font-weight: 600;
	color: var(--text-primary);
}

.dropdown-icon {
	width: 16px;
	height: 16px;
	color: var(--primary-color);
}

.close-btn {
	background: transparent;
	border: none;
	color: var(--text-secondary);
	cursor: pointer;
	padding: var(--spacing-xs);
	border-radius: var(--border-radius-sm);
	transition: all var(--transition-normal);
	display: flex;
	align-items: center;
	justify-content: center;
}

.close-btn:hover {
	color: var(--text-primary);
	background: var(--bg-tertiary);
}

.close-icon {
	width: 16px;
	height: 16px;
}

/* Список языков */
.languages-list {
	max-height: 300px;
	overflow-y: auto;
}

.language-option {
	display: flex;
	align-items: center;
	justify-content: space-between;
	width: 100%;
	padding: var(--spacing-md) var(--spacing-lg);
	background: transparent;
	border: none;
	color: var(--text-primary);
	cursor: pointer;
	transition: all var(--transition-normal);
	text-align: left;
	border-bottom: 1px solid var(--border-color);
}

.language-option:hover {
	background: var(--bg-secondary);
}

.language-option.active {
	background: var(--bg-elevated);
	border-left: 3px solid var(--primary-color);
}

.language-option.recommended {
	background: rgba(102, 126, 234, 0.05);
}

.language-option.recommended:hover {
	background: rgba(102, 126, 234, 0.1);
}

.language-info {
	display: flex;
	align-items: center;
	gap: var(--spacing-md);
	flex: 1;
}

.language-flag {
	font-size: 1.5em;
	width: 32px;
	text-align: center;
}

.language-details {
	display: flex;
	flex-direction: column;
	gap: var(--spacing-xs);
}

.language-name {
	font-weight: 600;
	font-size: 1em;
}

.language-native {
	font-size: 0.9em;
	color: var(--text-secondary);
	font-style: italic;
}

.language-status {
	display: flex;
	align-items: center;
	gap: var(--spacing-sm);
}

.recommended-badge {
	background: var(--primary-color);
	color: white;
	padding: var(--spacing-xs) var(--spacing-sm);
	border-radius: var(--border-radius-sm);
	font-size: 0.8em;
	font-weight: 500;
}

.check-icon {
	width: 16px;
	height: 16px;
	color: var(--success-color);
}

/* Футер dropdown */
.dropdown-footer {
	padding: var(--spacing-md) var(--spacing-lg);
	border-top: 1px solid var(--border-color);
	background: var(--bg-secondary);
}

.language-tip {
	color: var(--text-muted);
	font-size: 0.9em;
	font-style: italic;
	text-align: center;
	display: block;
}

/* Backdrop */
.dropdown-backdrop {
	position: fixed;
	top: 0;
	left: 0;
	right: 0;
	bottom: 0;
	background: transparent;
	z-index: 999;
}

/* Анимации */
.language-dropdown {
	animation: dropdownSlide var(--transition-normal) ease-out;
}

@keyframes dropdownSlide {
	from {
		opacity: 0;
		transform: translateY(-20px) scale(0.95);
	}
	to {
		opacity: 1;
		transform: translateY(0) scale(1);
	}
}

/* Hover эффекты */
.language-switcher:hover .language-toggle {
	border-color: var(--border-hover);
}

/* Фокус для доступности */
.language-toggle:focus-visible,
.language-option:focus-visible,
.close-btn:focus-visible {
	outline: 2px solid var(--border-focus);
	outline-offset: 2px;
}

/* Улучшения для высокого контраста */
@media (prefers-contrast: high) {
	.language-toggle,
	.language-dropdown {
		border: 2px solid var(--border-color);
	}

	.language-option {
		border-bottom: 2px solid var(--border-color);
	}

	.language-option.active {
		border-left: 4px solid var(--primary-color);
	}
}

/* Улучшения для уменьшенного движения */
@media (prefers-reduced-motion: reduce) {
	.language-dropdown,
	.chevron-icon {
		animation: none !important;
		transition: none !important;
	}
}

/* Адаптивность */
@media (max-width: 768px) {
	.language-dropdown {
		position: fixed;
		top: 50%;
		left: 50%;
		right: auto;
		transform: translate(-50%, -50%);
		min-width: 90vw;
		max-width: 400px;
		max-height: 80vh;
	}

	.language-dropdown.dropdown-open {
		transform: translate(-50%, -50%);
	}

	.language-toggle {
		padding: var(--spacing-sm);
	}

	.current-language {
		display: none;
	}
}

@media (max-width: 480px) {
	.language-dropdown {
		min-width: 95vw;
	}

	.dropdown-header,
	.language-option,
	.dropdown-footer {
		padding: var(--spacing-sm) var(--spacing-md);
	}

	.language-flag {
		font-size: 1.2em;
		width: 24px;
	}

	.language-name {
		font-size: 0.9em;
	}

	.language-native {
		font-size: 0.8em;
	}
}

/* Темная тема */
[data-theme="dark"] .language-switcher {
	background: var(--bg-secondary);
}

/* Светлая тема */
[data-theme="light"] .language-switcher {
	background: var(--bg-secondary-light);
}

/* Стили для скроллбара в dropdown */
.languages-list::-webkit-scrollbar {
	width: 6px;
}

.languages-list::-webkit-scrollbar-track {
	background: var(--bg-secondary);
	border-radius: 3px;
}

.languages-list::-webkit-scrollbar-thumb {
	background: var(--border-color);
	border-radius: 3px;
}

.languages-list::-webkit-scrollbar-thumb:hover {
	background: var(--border-hover);
}

/* Стили для состояния загрузки */
.language-switcher.loading {
	opacity: 0.7;
	pointer-events: none;
}

.language-switcher.loading::after {
	content: "";
	position: absolute;
	top: 50%;
	left: 50%;
	width: 20px;
	height: 20px;
	margin: -10px 0 0 -10px;
	border: 2px solid transparent;
	border-top: 2px solid var(--primary-color);
	border-radius: 50%;
	animation: spin 1s linear infinite;
}

@keyframes spin {
	0% {
		transform: rotate(0deg);
	}
	100% {
		transform: rotate(360deg);
	}
}

/* Стили для анимации появления языков */
.language-option {
	animation: languageSlide var(--transition-normal) ease-out;
	animation-fill-mode: both;
}

.language-option:nth-child(1) {
	animation-delay: 0.1s;
}
.language-option:nth-child(2) {
	animation-delay: 0.2s;
}
.language-option:nth-child(3) {
	animation-delay: 0.3s;
}
.language-option:nth-child(4) {
	animation-delay: 0.4s;
}
.language-option:nth-child(5) {
	animation-delay: 0.5s;
}
.language-option:nth-child(6) {
	animation-delay: 0.6s;
}
.language-option:nth-child(7) {
	animation-delay: 0.7s;
}

@keyframes languageSlide {
	from {
		opacity: 0;
		transform: translateX(-20px);
	}
	to {
		opacity: 1;
		transform: translateX(0);
	}
}

/* Стили для пустого состояния */
.languages-list:empty::before {
	content: "Нет доступных языков";
	display: block;
	text-align: center;
	padding: var(--spacing-xl);
	color: var(--text-muted);
	font-style: italic;
}

/* Стили для ошибок */
.language-switcher.error .language-toggle {
	border-color: var(--error-color);
	background: rgba(239, 68, 68, 0.1);
}

.language-switcher.error .language-toggle:hover {
	background: rgba(239, 68, 68, 0.2);
}

/* Стили для успешного выбора */
.language-switcher.success .language-toggle {
	border-color: var(--success-color);
	background: rgba(16, 185, 129, 0.1);
}

.language-switcher.success .language-toggle:hover {
	background: rgba(16, 185, 129, 0.2);
}
</style>
