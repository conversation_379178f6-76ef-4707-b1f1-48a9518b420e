import { BasePlugin } from '../utils/BasePlugin.js';
import BackgroundComponent from '../widgets/BackgroundWidget.vue';
import BackgroundSettings from '../components/BackgroundSettings.vue';

/**
 * Плагин управления фоном Veyra
 * Поддерживает цвета, градиенты, Unsplash API и анимации
 */
class BackgroundPluginClass extends BasePlugin {
  constructor() {
    super({
      id: 'background',
      name: 'Фон',
      version: '1.0.0',
      description: 'Управление фоном страницы: цвета, градиенты, изображения',
      component: BackgroundComponent,
      settingsComponent: BackgroundSettings,
      defaultSettings: {
        type: 'gradient', // 'solid', 'gradient', 'image', 'unsplash'
        
        // Настройки сплошного цвета
        solidColor: '#8B45C7',
        
        // Настройки градиента
        gradientType: 'linear', // 'linear', 'radial', 'conic'
        gradientDirection: '45deg',
        gradientColors: [
          { color: '#8B45C7', position: 0 },
          { color: '#6366F1', position: 100 }
        ],
        
        // Настройки изображения
        imageUrl: '',
        imageSize: 'cover', // 'cover', 'contain', 'auto'
        imagePosition: 'center',
        imageRepeat: 'no-repeat',
        
        // Настройки Unsplash
        unsplashQuery: 'nature',
        unsplashCollection: '',
        unsplashOrientation: 'landscape', // 'landscape', 'portrait', 'squarish'
        
        // Анимации
        enableAnimation: false,
        animationType: 'rotate', // 'rotate', 'pulse', 'wave'
        animationSpeed: 'normal', // 'slow', 'normal', 'fast'
        
        // Дополнительные эффекты
        overlay: false,
        overlayColor: 'rgba(0, 0, 0, 0.3)',
        blur: 0, // 0-20px
        brightness: 100, // 0-200%
        contrast: 100, // 0-200%
        saturation: 100 // 0-200%
      }
    });
    
    this.unsplashApiKey = null;
    this.currentImageUrl = null;
    this.animationFrame = null;
  }

  async onInit() {
    // Загружаем API ключ Unsplash из настроек расширения
    await this.loadUnsplashApiKey();
    
    // Если тип фона - Unsplash, загружаем изображение
    if (this.getSetting('type') === 'unsplash') {
      await this.loadUnsplashImage();
    }
    
    // Запускаем анимацию если включена
    if (this.getSetting('enableAnimation')) {
      this.startAnimation();
    }
    
    console.log('Background plugin initialized');
  }

  async onDestroy() {
    // Останавливаем анимацию
    this.stopAnimation();
    console.log('Background plugin destroyed');
  }

  /**
   * Загружает API ключ Unsplash
   */
  async loadUnsplashApiKey() {
    try {
      const result = await chrome.storage.sync.get('unsplashApiKey');
      this.unsplashApiKey = result.unsplashApiKey || null;
    } catch (error) {
      console.error('Failed to load Unsplash API key:', error);
    }
  }

  /**
   * Загружает случайное изображение с Unsplash
   */
  async loadUnsplashImage() {
    if (!this.unsplashApiKey) {
      console.warn('Unsplash API key not configured');
      return;
    }

    try {
      const query = this.getSetting('unsplashQuery');
      const collection = this.getSetting('unsplashCollection');
      const orientation = this.getSetting('unsplashOrientation');
      
      let url = `https://api.unsplash.com/photos/random?client_id=${this.unsplashApiKey}`;
      
      if (query) url += `&query=${encodeURIComponent(query)}`;
      if (collection) url += `&collections=${collection}`;
      if (orientation) url += `&orientation=${orientation}`;
      
      const response = await fetch(url);
      const data = await response.json();
      
      if (data.urls && data.urls.full) {
        this.currentImageUrl = data.urls.full;
        await this.setSetting('imageUrl', this.currentImageUrl);
        this.emit('image-loaded', { url: this.currentImageUrl, data });
      }
    } catch (error) {
      console.error('Failed to load Unsplash image:', error);
    }
  }

  /**
   * Генерирует CSS для фона
   */
  generateBackgroundCSS() {
    const type = this.getSetting('type');
    const settings = this.getSettings();
    
    let css = '';
    
    switch (type) {
      case 'solid':
        css = `background: ${settings.solidColor};`;
        break;
        
      case 'gradient':
        css = this.generateGradientCSS(settings);
        break;
        
      case 'image':
      case 'unsplash':
        const imageUrl = type === 'unsplash' ? this.currentImageUrl : settings.imageUrl;
        if (imageUrl) {
          css = `
            background-image: url('${imageUrl}');
            background-size: ${settings.imageSize};
            background-position: ${settings.imagePosition};
            background-repeat: ${settings.imageRepeat};
          `;
        }
        break;
    }
    
    // Добавляем эффекты
    if (settings.overlay) {
      css += `background-blend-mode: overlay;`;
      css += `background-color: ${settings.overlayColor};`;
    }
    
    // Добавляем фильтры
    const filters = [];
    if (settings.blur > 0) filters.push(`blur(${settings.blur}px)`);
    if (settings.brightness !== 100) filters.push(`brightness(${settings.brightness}%)`);
    if (settings.contrast !== 100) filters.push(`contrast(${settings.contrast}%)`);
    if (settings.saturation !== 100) filters.push(`saturate(${settings.saturation}%)`);
    
    if (filters.length > 0) {
      css += `filter: ${filters.join(' ')};`;
    }
    
    return css;
  }

  /**
   * Генерирует CSS для градиента
   */
  generateGradientCSS(settings) {
    const { gradientType, gradientDirection, gradientColors } = settings;
    
    const colorStops = gradientColors
      .sort((a, b) => a.position - b.position)
      .map(stop => `${stop.color} ${stop.position}%`)
      .join(', ');
    
    switch (gradientType) {
      case 'linear':
        return `background: linear-gradient(${gradientDirection}, ${colorStops});`;
        
      case 'radial':
        return `background: radial-gradient(circle, ${colorStops});`;
        
      case 'conic':
        return `background: conic-gradient(from ${gradientDirection}, ${colorStops});`;
        
      default:
        return `background: linear-gradient(45deg, ${colorStops});`;
    }
  }

  /**
   * Запускает анимацию фона
   */
  startAnimation() {
    const animationType = this.getSetting('animationType');
    const animationSpeed = this.getSetting('animationSpeed');
    
    // Определяем скорость анимации
    let duration;
    switch (animationSpeed) {
      case 'slow': duration = 10000; break;
      case 'fast': duration = 2000; break;
      default: duration = 5000; break;
    }
    
    // Запускаем соответствующую анимацию
    switch (animationType) {
      case 'rotate':
        this.startRotateAnimation(duration);
        break;
      case 'pulse':
        this.startPulseAnimation(duration);
        break;
      case 'wave':
        this.startWaveAnimation(duration);
        break;
    }
  }

  /**
   * Останавливает анимацию фона
   */
  stopAnimation() {
    if (this.animationFrame) {
      cancelAnimationFrame(this.animationFrame);
      this.animationFrame = null;
    }
  }

  /**
   * Анимация вращения градиента
   */
  startRotateAnimation(duration) {
    let startTime = null;
    
    const animate = (timestamp) => {
      if (!startTime) startTime = timestamp;
      const progress = (timestamp - startTime) / duration;
      
      if (this.getSetting('gradientType') === 'linear') {
        const angle = (progress * 360) % 360;
        this.setSetting('gradientDirection', `${angle}deg`);
      }
      
      this.animationFrame = requestAnimationFrame(animate);
    };
    
    this.animationFrame = requestAnimationFrame(animate);
  }

  /**
   * Анимация пульсации
   */
  startPulseAnimation(duration) {
    // Реализация пульсации
    // TODO: Добавить логику пульсации
  }

  /**
   * Волновая анимация
   */
  startWaveAnimation(duration) {
    // Реализация волновой анимации
    // TODO: Добавить логику волн
  }

  /**
   * Обновляет тип фона
   */
  async setBackgroundType(type) {
    await this.setSetting('type', type);
    
    if (type === 'unsplash') {
      await this.loadUnsplashImage();
    }
    
    this.emit('background-changed', { type });
  }

  /**
   * Добавляет цвет в градиент
   */
  async addGradientColor(color, position) {
    const colors = this.getSetting('gradientColors');
    colors.push({ color, position });
    colors.sort((a, b) => a.position - b.position);
    await this.setSetting('gradientColors', colors);
  }

  /**
   * Удаляет цвет из градиента
   */
  async removeGradientColor(index) {
    const colors = this.getSetting('gradientColors');
    if (colors.length > 2) { // Минимум 2 цвета
      colors.splice(index, 1);
      await this.setSetting('gradientColors', colors);
    }
  }

  /**
   * Валидирует настройки плагина
   */
  validateSettings(settings) {
    // Проверяем обязательные поля
    if (!settings.type || !['solid', 'gradient', 'image', 'unsplash'].includes(settings.type)) {
      return false;
    }
    
    // Проверяем градиентные цвета
    if (settings.gradientColors && (!Array.isArray(settings.gradientColors) || settings.gradientColors.length < 2)) {
      return false;
    }
    
    return true;
  }
}

// Создаем экземпляр плагина
export const BackgroundPlugin = new BackgroundPluginClass();
