import { reactive, watch } from "vue";

const defaultSettings = {
	showTime: true,
	showLinks: true,
	language: "ru", // Добавлено поле языка
	backgroundType: "gradient", // solid | gradient
	solidColor: "#333333",
	gradient: {
		type: "linear", // linear | radial | conic
		angle: 45,
		colors: ["#ff8c00", "#ff0080"],
		animation: true,
		animationSpeed: 20, // секунд на полный цикл
		animationTiming: "linear", // 👈 новое поле
	},
	links: [{ label: "GitHub", url: "https://github.com" }],
	openWeatherApiKey: "", // Добавляем поле для API ключа
	weatherLocation: "Moscow", // Город по умолчанию
	activeWidgets: [ // Добавлено поле для активных виджетов
		{ id: 'time', active: true, component: 'TimeDisplay' },
		{ id: 'links', active: true, component: 'LinksDisplay' },
	],
};

function validateSettings(settings) {
  // Проверяем обязательные поля
  if (typeof settings !== 'object') return false;
  
  const requiredKeys = ['showTime', 'showLinks', 'backgroundType', 'solidColor', 'gradient', 'links'];
  return requiredKeys.every(key => key in settings);
}

function migrateSettings(oldSettings) {
  // Пример миграции: если в старых настройках нет нового поля, добавляем его
  if (!('language' in oldSettings)) {
    oldSettings.language = 'ru';
  }
  return oldSettings;
}

const storedSettings = JSON.parse(localStorage.getItem("settings")) || {};
const initialSettings = validateSettings(storedSettings) 
  ? migrateSettings(storedSettings)
  : defaultSettings;

export const settings = reactive(initialSettings);

watch(
	settings,
	(val) => {
    if (validateSettings(val)) {
      localStorage.setItem("settings", JSON.stringify(val));
    } else {
      console.error("Invalid settings structure", val);
    }
	},
	{ deep: true }
);
