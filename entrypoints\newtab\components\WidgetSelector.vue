<template>
	<div class="widget-selector widget">
		<div class="selector-header">
			<h3 class="selector-title">
				<Layout class="selector-icon" />
				{{ $t("widgets.title") }}
			</h3>
			<div class="widget-count">
				{{ activeWidgets.length }} {{ $t("widgets.active") }}
			</div>
		</div>

		<div class="selector-content">
			<!-- Активные виджеты -->
			<div class="active-widgets-section">
				<h4 class="section-title">
					<Eye class="section-icon" />
					{{ $t("widgets.activeWidgets") }}
				</h4>

				<draggable
					v-model="activeWidgets"
					class="widgets-list active-list"
					group="widgets"
					item-key="id"
					@end="updateWidgetOrder"
					:animation="200"
					ghost-class="widget-ghost"
					chosen-class="widget-chosen"
				>
					<template #item="{ element: widget }">
						<div class="widget-item active-widget">
							<div class="widget-drag-handle">
								<GripVertical class="drag-icon" />
							</div>

							<div class="widget-info">
								<div class="widget-icon">
									<component
										:is="getWidgetIcon(widget.component)"
										class="widget-icon-svg"
									/>
								</div>
								<div class="widget-details">
									<div class="widget-name">
										{{ getWidgetLabel(widget.component) }}
									</div>
									<div class="widget-description">
										{{ getWidgetDescription(widget.component) }}
									</div>
								</div>
							</div>

							<div class="widget-actions">
								<button
									@click="deactivateWidget(widget.id)"
									class="widget-btn deactivate-btn"
									:title="$t('widgets.deactivate')"
									:aria-label="$t('widgets.deactivate')"
								>
									<EyeOff class="action-icon" />
								</button>

								<button
									@click="customizeWidget(widget.id)"
									class="widget-btn customize-btn"
									:title="$t('widgets.customize')"
									:aria-label="$t('widgets.customize')"
								>
									<Settings class="action-icon" />
								</button>
							</div>
						</div>
					</template>
				</draggable>
			</div>

			<!-- Неактивные виджеты -->
			<div class="inactive-widgets-section">
				<h4 class="section-title">
					<EyeOff class="section-icon" />
					{{ $t("widgets.availableWidgets") }}
				</h4>

				<div class="widgets-grid">
					<div
						v-for="widget in availableWidgets"
						:key="widget.id"
						class="widget-card inactive-widget"
						:class="{ 'widget-customizable': widget.customizable }"
					>
						<div class="widget-card-header">
							<div class="widget-card-icon">
								<component :is="widget.icon" class="widget-card-icon-svg" />
							</div>
							<div class="widget-card-badge" v-if="widget.customizable">
								<Settings class="badge-icon" />
							</div>
						</div>

						<div class="widget-card-content">
							<h5 class="widget-card-title">{{ widget.label }}</h5>
							<p class="widget-card-description">{{ widget.description }}</p>
							<div class="widget-card-category">{{ widget.category }}</div>
						</div>

						<div class="widget-card-actions">
							<button
								@click="activateWidget(widget.id)"
								class="btn btn-primary btn-sm"
								:title="$t('widgets.activate')"
								:aria-label="$t('widgets.activate')"
							>
								<Plus class="action-icon" />
								{{ $t("widgets.activate") }}
							</button>
						</div>
					</div>
				</div>
			</div>
		</div>

		<div class="selector-footer">
			<div class="footer-actions">
				<button
					@click="resetToDefaults"
					class="btn btn-ghost btn-sm"
					:title="$t('widgets.resetToDefaults')"
				>
					<RotateCcw class="action-icon" />
					{{ $t("widgets.resetToDefaults") }}
				</button>

				<button
					@click="exportConfiguration"
					class="btn btn-ghost btn-sm"
					:title="$t('widgets.exportConfig')"
				>
					<Download class="action-icon" />
					{{ $t("widgets.exportConfig") }}
				</button>
			</div>

			<div class="footer-info">
				<small class="widgets-tip">
					{{ $t("widgets.dragToReorder") }}
				</small>
			</div>
		</div>
	</div>
</template>

<script setup>
import { computed } from "vue";
import { useSettingsStore } from "../stores/settingsStore";
import { useWidgetsStore } from "../stores/widgetsStore";
import draggable from "vuedraggable";
import {
	Eye,
	EyeOff,
	GripVertical,
	Settings,
	Plus,
	RotateCcw,
	Layout,
	Download,
} from "lucide-vue-next";

const settingsStore = useSettingsStore();
const widgetsStore = useWidgetsStore();

// Вычисляемые свойства
const activeWidgets = computed({
	get: () => settingsStore.settings.activeWidgets,
	set: (value) => settingsStore.updateActiveWidgets(value),
});

const availableWidgets = computed(() => {
	const activeIds = activeWidgets.value.map((w) => w.id);
	return widgetsStore.widgets.filter(
		(widget) => !activeIds.includes(widget.id)
	);
});

// Методы
const getWidgetIcon = (componentName) => {
	const widget = widgetsStore.widgets.find(
		(w) => w.component === componentName
	);
	return widget ? widget.icon : "Layout";
};

const getWidgetLabel = (componentName) => {
	const widget = widgetsStore.widgets.find(
		(w) => w.component === componentName
	);
	return widget ? widget.label : componentName;
};

const getWidgetDescription = (componentName) => {
	const widget = widgetsStore.widgets.find(
		(w) => w.component === componentName
	);
	return widget ? widget.description : "";
};

const activateWidget = (widgetId) => {
	const widget = widgetsStore.widgets.find((w) => w.id === widgetId);
	if (widget) {
		const newWidget = {
			id: widget.id,
			active: true,
			component: widget.component,
		};
		const updatedWidgets = [...activeWidgets.value, newWidget];
		settingsStore.updateActiveWidgets(updatedWidgets);
	}
};

const deactivateWidget = (widgetId) => {
	const updatedWidgets = activeWidgets.value.filter((w) => w.id !== widgetId);
	settingsStore.updateActiveWidgets(updatedWidgets);
};

const updateWidgetOrder = () => {
	// Порядок уже обновлен через v-model
	settingsStore.updateActiveWidgets(activeWidgets.value);
};

const customizeWidget = (widgetId) => {
	// Здесь можно открыть модальное окно для настройки виджета
	console.log("Настройка виджета:", widgetId);
};

const resetToDefaults = () => {
	if (
		confirm(
			"Вы уверены, что хотите сбросить все виджеты к настройкам по умолчанию?"
		)
	) {
		const defaultWidgets = [
			{ id: "time", active: true, component: "TimeDisplay" },
			{ id: "links", active: true, component: "LinksDisplay" },
		];
		settingsStore.updateActiveWidgets(defaultWidgets);
	}
};

const exportConfiguration = () => {
	const config = {
		activeWidgets: activeWidgets.value,
		widgetSettings: Object.fromEntries(widgetsStore.widgetSettings),
	};

	const dataStr = JSON.stringify(config, null, 2);
	const dataBlob = new Blob([dataStr], { type: "application/json" });
	const url = URL.createObjectURL(dataBlob);
	const link = document.createElement("a");
	link.href = url;
	link.download = "veyra-widgets-config.json";
	link.click();
	URL.revokeObjectURL(url);
};
</script>

<style scoped>
.widget-selector {
	min-width: var(--widget-min-width);
	max-width: var(--widget-max-width);
}

.selector-header {
	display: flex;
	align-items: center;
	justify-content: space-between;
	margin-bottom: var(--spacing-md);
	padding-bottom: var(--spacing-sm);
	border-bottom: 1px solid var(--border-color);
}

.selector-title {
	display: flex;
	align-items: center;
	gap: var(--spacing-sm);
	margin: 0;
	font-size: 1.1em;
	font-weight: 600;
	color: var(--text-primary);
}

.selector-icon {
	color: var(--primary-color);
}

.widget-count {
	color: var(--text-secondary);
	font-size: 0.9em;
	background: var(--bg-tertiary);
	padding: var(--spacing-xs) var(--spacing-sm);
	border-radius: var(--border-radius-sm);
}

/* Контент селектора */
.selector-content {
	display: flex;
	flex-direction: column;
	gap: var(--spacing-lg);
}

/* Секции */
.section-title {
	display: flex;
	align-items: center;
	gap: var(--spacing-sm);
	margin: 0 0 var(--spacing-md) 0;
	font-size: 1em;
	font-weight: 600;
	color: var(--text-primary);
	padding-bottom: var(--spacing-sm);
	border-bottom: 1px solid var(--border-color);
}

.section-icon {
	width: 16px;
	height: 16px;
	color: var(--primary-color);
}

/* Активные виджеты */
.active-widgets-section {
	background: var(--bg-tertiary);
	border: 1px solid var(--border-color);
	border-radius: var(--border-radius-md);
	padding: var(--spacing-md);
}

.widgets-list {
	display: flex;
	flex-direction: column;
	gap: var(--spacing-sm);
}

.widget-item {
	display: flex;
	align-items: center;
	gap: var(--spacing-md);
	padding: var(--spacing-md);
	background: var(--bg-secondary);
	border: 1px solid var(--border-color);
	border-radius: var(--border-radius-md);
	transition: all var(--transition-normal);
	cursor: move;
}

.widget-item:hover {
	border-color: var(--border-hover);
	box-shadow: 0 2px 8px var(--shadow-color);
	transform: translateY(-1px);
}

.widget-item.active-widget {
	border-left: 4px solid var(--success-color);
}

.widget-drag-handle {
	display: flex;
	align-items: center;
	justify-content: center;
	color: var(--text-muted);
	cursor: grab;
	padding: var(--spacing-xs);
	border-radius: var(--border-radius-sm);
	transition: all var(--transition-normal);
}

.widget-drag-handle:hover {
	color: var(--text-primary);
	background: var(--bg-elevated);
}

.widget-drag-handle:active {
	cursor: grabbing;
}

.drag-icon {
	width: 16px;
	height: 16px;
}

.widget-info {
	display: flex;
	align-items: center;
	gap: var(--spacing-md);
	flex: 1;
}

.widget-icon {
	width: 32px;
	height: 32px;
	display: flex;
	align-items: center;
	justify-content: center;
	background: var(--bg-elevated);
	border-radius: var(--border-radius-sm);
	color: var(--primary-color);
}

.widget-icon-svg {
	width: 20px;
	height: 20px;
}

.widget-details {
	flex: 1;
}

.widget-name {
	color: var(--text-primary);
	font-weight: 600;
	font-size: 1em;
	margin-bottom: var(--spacing-xs);
}

.widget-description {
	color: var(--text-secondary);
	font-size: 0.9em;
	line-height: 1.4;
}

.widget-actions {
	display: flex;
	gap: var(--spacing-xs);
	flex-shrink: 0;
}

.widget-btn {
	background: transparent;
	border: none;
	color: var(--text-secondary);
	cursor: pointer;
	padding: var(--spacing-xs);
	border-radius: var(--border-radius-sm);
	transition: all var(--transition-normal);
	display: flex;
	align-items: center;
	justify-content: center;
}

.widget-btn:hover {
	color: var(--text-primary);
	background: var(--bg-elevated);
}

.deactivate-btn:hover {
	color: var(--warning-color);
}

.customize-btn:hover {
	color: var(--info-color);
}

.action-icon {
	width: 16px;
	height: 16px;
}

/* Неактивные виджеты */
.inactive-widgets-section {
	background: var(--bg-tertiary);
	border: 1px solid var(--border-color);
	border-radius: var(--border-radius-md);
	padding: var(--spacing-md);
}

.widgets-grid {
	display: grid;
	grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
	gap: var(--spacing-md);
}

.widget-card {
	background: var(--bg-secondary);
	border: 1px solid var(--border-color);
	border-radius: var(--border-radius-md);
	padding: var(--spacing-md);
	transition: all var(--transition-normal);
	position: relative;
	overflow: hidden;
}

.widget-card:hover {
	border-color: var(--border-hover);
	box-shadow: 0 4px 12px var(--shadow-color);
	transform: translateY(-2px);
}

.widget-card.inactive-widget {
	opacity: 0.8;
}

.widget-card.inactive-widget:hover {
	opacity: 1;
}

.widget-card-customizable {
	border-color: var(--primary-color);
}

.widget-card-customizable::before {
	content: "";
	position: absolute;
	top: 0;
	left: 0;
	right: 0;
	height: 2px;
	background: var(--primary-color);
}

.widget-card-header {
	display: flex;
	align-items: center;
	justify-content: space-between;
	margin-bottom: var(--spacing-md);
}

.widget-card-icon {
	width: 48px;
	height: 48px;
	display: flex;
	align-items: center;
	justify-content: center;
	background: var(--bg-elevated);
	border-radius: var(--border-radius-md);
	color: var(--primary-color);
}

.widget-card-icon-svg {
	width: 24px;
	height: 24px;
}

.widget-card-badge {
	background: var(--primary-color);
	color: white;
	padding: var(--spacing-xs);
	border-radius: var(--border-radius-sm);
	display: flex;
	align-items: center;
	justify-content: center;
}

.badge-icon {
	width: 12px;
	height: 12px;
}

.widget-card-content {
	margin-bottom: var(--spacing-md);
}

.widget-card-title {
	margin: 0 0 var(--spacing-xs) 0;
	color: var(--text-primary);
	font-size: 1em;
	font-weight: 600;
}

.widget-card-description {
	margin: 0 0 var(--spacing-sm) 0;
	color: var(--text-secondary);
	font-size: 0.9em;
	line-height: 1.4;
}

.widget-card-category {
	display: inline-block;
	background: var(--bg-tertiary);
	color: var(--text-muted);
	padding: var(--spacing-xs) var(--spacing-sm);
	border-radius: var(--border-radius-sm);
	font-size: 0.8em;
	text-transform: capitalize;
}

.widget-card-actions {
	display: flex;
	justify-content: center;
}

/* Футер */
.selector-footer {
	display: flex;
	flex-direction: column;
	gap: var(--spacing-md);
	margin-top: var(--spacing-md);
	padding-top: var(--spacing-md);
	border-top: 1px solid var(--border-color);
}

.footer-actions {
	display: flex;
	gap: var(--spacing-sm);
	justify-content: center;
}

.footer-info {
	text-align: center;
}

.widgets-tip {
	color: var(--text-muted);
	font-size: 0.9em;
	font-style: italic;
}

/* Drag and Drop стили */
.widget-ghost {
	opacity: 0.5;
	background: var(--bg-elevated);
	border: 2px dashed var(--primary-color);
}

.widget-chosen {
	background: var(--bg-elevated);
	border-color: var(--primary-color);
	box-shadow: 0 4px 12px var(--shadow-color);
}

/* Анимации */
.widget-item,
.widget-card {
	animation: slideIn var(--transition-normal) ease-out;
}

@keyframes slideIn {
	from {
		opacity: 0;
		transform: translateY(10px);
	}
	to {
		opacity: 1;
		transform: translateY(0);
	}
}

/* Hover эффекты */
.widget-selector:hover {
	transform: translateY(-2px);
	box-shadow: 0 8px 25px var(--shadow-elevated);
}

/* Фокус для доступности */
.widget-btn:focus-visible,
.btn:focus-visible {
	outline: 2px solid var(--border-focus);
	outline-offset: 2px;
}

/* Улучшения для высокого контраста */
@media (prefers-contrast: high) {
	.widget-item,
	.widget-card {
		border: 2px solid var(--border-color);
	}

	.widget-btn {
		border: 1px solid var(--border-color);
	}
}

/* Улучшения для уменьшенного движения */
@media (prefers-reduced-motion: reduce) {
	.widget-item,
	.widget-card,
	.widget-selector {
		animation: none !important;
		transition: none !important;
	}
}

/* Адаптивность */
@media (max-width: 768px) {
	.widget-selector {
		min-width: 100%;
	}

	.widgets-grid {
		grid-template-columns: 1fr;
	}

	.widget-item {
		flex-direction: column;
		align-items: flex-start;
		gap: var(--spacing-sm);
	}

	.widget-info {
		width: 100%;
	}

	.widget-actions {
		align-self: flex-end;
	}

	.footer-actions {
		flex-direction: column;
		align-items: center;
	}
}

@media (max-width: 480px) {
	.widget-card {
		padding: var(--spacing-sm);
	}

	.widget-card-icon {
		width: 40px;
		height: 40px;
	}

	.widget-card-icon-svg {
		width: 20px;
		height: 20px;
	}
}

/* Темная тема */
[data-theme="dark"] .widget-selector {
	background: var(--bg-secondary);
}

/* Светлая тема */
[data-theme="light"] .widget-selector {
	background: var(--bg-secondary-light);
}

/* Стили для состояния загрузки */
.widget-selector.loading {
	opacity: 0.7;
	pointer-events: none;
}

.widget-selector.loading::after {
	content: "";
	position: absolute;
	top: 50%;
	left: 50%;
	width: 30px;
	height: 30px;
	margin: -15px 0 0 -15px;
	border: 3px solid transparent;
	border-top: 3px solid var(--primary-color);
	border-radius: 50%;
	animation: spin 1s linear infinite;
}

@keyframes spin {
	0% {
		transform: rotate(0deg);
	}
	100% {
		transform: rotate(360deg);
	}
}

/* Стили для анимации появления карточек */
.widget-card:nth-child(1) {
	animation-delay: 0.1s;
}
.widget-card:nth-child(2) {
	animation-delay: 0.2s;
}
.widget-card:nth-child(3) {
	animation-delay: 0.3s;
}
.widget-card:nth-child(4) {
	animation-delay: 0.4s;
}
.widget-card:nth-child(5) {
	animation-delay: 0.5s;
}

/* Стили для пустого состояния */
.widgets-list:empty::before {
	content: "Нет активных виджетов";
	display: block;
	text-align: center;
	padding: var(--spacing-xl);
	color: var(--text-muted);
	font-style: italic;
}

.widgets-grid:empty::before {
	content: "Нет доступных виджетов";
	display: block;
	text-align: center;
	padding: var(--spacing-xl);
	color: var(--text-muted);
	font-style: italic;
}
</style>
