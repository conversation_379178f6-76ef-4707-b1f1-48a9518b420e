@import url('https://fonts.googleapis.com/css2?family=Inter:wght@100..900&display=swap');

/* CSS переменные для темизации */
:root {
  /* Основные цвета */
  --primary-color: #667eea;
  --primary-hover: #5a6fd8;
  --secondary-color: #764ba2;
  --accent-color: #f093fb;

  /* Цвета успеха/ошибки */
  --success-color: #10b981;
  --warning-color: #f59e0b;
  --error-color: #ef4444;
  --info-color: #3b82f6;

  /* Темная тема */
  --bg-primary: #0f0f23;
  --bg-secondary: #1a1a2e;
  --bg-tertiary: #16213e;
  --bg-elevated: #0f3460;

  --text-primary: #ffffff;
  --text-secondary: #a1a1aa;
  --text-muted: #71717a;

  --border-color: #27272a;
  --border-hover: #3f3f46;
  --border-focus: #667eea;

  --shadow-color: rgba(0, 0, 0, 0.3);
  --shadow-elevated: rgba(0, 0, 0, 0.5);

  /* Светлая тема */
  --bg-primary-light: #ffffff;
  --bg-secondary-light: #f8fafc;
  --bg-tertiary-light: #f1f5f9;
  --bg-elevated-light: #e2e8f0;

  --text-primary-light: #0f172a;
  --text-secondary-light: #475569;
  --text-muted-light: #64748b;

  --border-color-light: #e2e8f0;
  --border-hover-light: #cbd5e1;
  --border-focus-light: #667eea;

  --shadow-color-light: rgba(0, 0, 0, 0.1);
  --shadow-elevated-light: rgba(0, 0, 0, 0.15);

  /* Общие переменные */
  --transition-fast: 0.15s ease;
  --transition-normal: 0.25s ease;
  --transition-slow: 0.35s ease;

  --border-radius-sm: 6px;
  --border-radius-md: 8px;
  --border-radius-lg: 12px;
  --border-radius-xl: 16px;

  --spacing-xs: 4px;
  --spacing-sm: 8px;
  --spacing-md: 16px;
  --spacing-lg: 24px;
  --spacing-xl: 32px;

  /* Размеры */
  --header-height: 60px;
  --sidebar-width: 280px;
  --widget-min-width: 280px;
  --widget-max-width: 400px;
}

/* Темная тема (по умолчанию) */
[data-theme="dark"] {
  --bg-primary: #0f0f23;
  --bg-secondary: #1a1a2e;
  --bg-tertiary: #16213e;
  --bg-elevated: #0f3460;

  --text-primary: #ffffff;
  --text-secondary: #a1a1aa;
  --text-muted: #71717a;

  --border-color: #27272a;
  --border-hover: #3f3f46;
  --border-focus: #667eea;

  --shadow-color: rgba(0, 0, 0, 0.3);
  --shadow-elevated: rgba(0, 0, 0, 0.5);
}

/* Светлая тема */
[data-theme="light"] {
  --bg-primary: #ffffff;
  --bg-secondary: #f8fafc;
  --bg-tertiary: #f1f5f9;
  --bg-elevated: #e2e8f0;

  --text-primary: #0f172a;
  --text-secondary: #475569;
  --text-muted: #64748b;

  --border-color: #e2e8f0;
  --border-hover: #cbd5e1;
  --border-focus: #667eea;

  --shadow-color: rgba(0, 0, 0, 0.1);
  --shadow-elevated: rgba(0, 0, 0, 0.15);
}

/* Базовые стили */
* {
  box-sizing: border-box;
}

body {
  font-family: "Inter", system-ui, -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, 'Open Sans', 'Helvetica Neue', sans-serif;
  margin: 0;
  line-height: 1.6;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  background: var(--bg-primary);
  color: var(--text-primary);
  transition: background-color var(--transition-normal), color var(--transition-normal);
}

input,
button,
select,
textarea {
  font-family: inherit;
  font-size: inherit;
}

/* Основной контейнер */
#app {
  height: 100vh;
  width: 100vw;
  overflow: hidden;
  margin: 0;
  padding: 0;
  font-size: 14px;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  gap: var(--spacing-lg);
  position: relative;
  background: var(--bg-primary);
}

/* Улучшенные стили для форм */
input,
select,
textarea {
  padding: var(--spacing-sm) var(--spacing-md);
  border-radius: var(--border-radius-md);
  border: 1px solid var(--border-color);
  background: var(--bg-secondary);
  color: var(--text-primary);
  transition: all var(--transition-normal);
  font-size: 14px;
}

input:focus,
select:focus,
textarea:focus {
  outline: none;
  border-color: var(--border-focus);
  box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
  background: var(--bg-tertiary);
}

input::placeholder,
textarea::placeholder {
  color: var(--text-muted);
}

/* Улучшенные стили для кнопок */
.btn {
  padding: var(--spacing-sm) var(--spacing-md);
  background: var(--bg-secondary);
  border: 1px solid var(--border-color);
  border-radius: var(--border-radius-md);
  color: var(--text-primary);
  cursor: pointer;
  font-weight: 500;
  transition: all var(--transition-normal);
  display: inline-flex;
  align-items: center;
  justify-content: center;
  gap: var(--spacing-sm);
  text-decoration: none;
  user-select: none;
  position: relative;
  overflow: hidden;
}

.btn::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.1), transparent);
  transition: left var(--transition-normal);
}

.btn:hover {
  background: var(--bg-tertiary);
  border-color: var(--border-hover);
  transform: translateY(-1px);
  box-shadow: 0 4px 12px var(--shadow-color);
}

.btn:hover::before {
  left: 100%;
}

.btn:active {
  transform: translateY(0);
  box-shadow: 0 2px 6px var(--shadow-color);
}

.btn:focus {
  outline: none;
  box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.2);
}

/* Варианты кнопок */
.btn-primary {
  background: var(--primary-color);
  border-color: var(--primary-color);
  color: white;
}

.btn-primary:hover {
  background: var(--primary-hover);
  border-color: var(--primary-hover);
}

.btn-success {
  background: var(--success-color);
  border-color: var(--success-color);
  color: white;
}

.btn-success:hover {
  background: #059669;
  border-color: #059669;
}

.btn-danger {
  background: var(--error-color);
  border-color: var(--error-color);
  color: white;
}

.btn-danger:hover {
  background: #dc2626;
  border-color: #dc2626;
}

.btn-ghost {
  background: transparent;
  border-color: transparent;
}

.btn-ghost:hover {
  background: var(--bg-secondary);
  border-color: var(--border-color);
}

/* Стили для уведомлений */
.notification {
  position: fixed;
  top: var(--spacing-lg);
  right: var(--spacing-lg);
  padding: var(--spacing-md) var(--spacing-lg);
  border-radius: var(--border-radius-md);
  color: white;
  font-weight: 500;
  z-index: 1000;
  animation: slideIn var(--transition-normal) ease-out;
  max-width: 300px;
  box-shadow: 0 4px 12px var(--shadow-color);
}

.notification-success {
  background: var(--success-color);
}

.notification-error {
  background: var(--error-color);
}

.notification-info {
  background: var(--info-color);
}

.notification-warning {
  background: var(--warning-color);
}

@keyframes slideIn {
  from {
    transform: translateX(100%);
    opacity: 0;
  }

  to {
    transform: translateX(0);
    opacity: 1;
  }
}

/* Улучшенные стили для чекбоксов */
input[type="checkbox"] {
  width: 18px;
  height: 18px;
  accent-color: var(--primary-color);
  cursor: pointer;
}

input[type="radio"] {
  width: 16px;
  height: 16px;
  accent-color: var(--primary-color);
  cursor: pointer;
}

/* Улучшенные стили для range input */
input[type="range"] {
  -webkit-appearance: none;
  appearance: none;
  height: 6px;
  background: var(--bg-tertiary);
  border-radius: 3px;
  outline: none;
  cursor: pointer;
}

input[type="range"]::-webkit-slider-thumb {
  -webkit-appearance: none;
  appearance: none;
  width: 18px;
  height: 18px;
  background: var(--primary-color);
  border-radius: 50%;
  cursor: pointer;
  transition: all var(--transition-normal);
}

input[type="range"]::-webkit-slider-thumb:hover {
  background: var(--primary-hover);
  transform: scale(1.1);
}

input[type="range"]::-moz-range-thumb {
  width: 18px;
  height: 18px;
  background: var(--primary-color);
  border-radius: 50%;
  cursor: pointer;
  border: none;
  transition: all var(--transition-normal);
}

input[type="range"]::-moz-range-thumb:hover {
  background: var(--primary-hover);
  transform: scale(1.1);
}

/* Улучшенные стили для color input */
input[type="color"] {
  padding: 0;
  -webkit-appearance: none;
  -moz-appearance: none;
  appearance: none;
  background-color: transparent;
  border: 2px solid var(--border-color);
  border-radius: var(--border-radius-md);
  block-size: 36px;
  inline-size: 60px;
  cursor: pointer;
  transition: border-color var(--transition-normal);
}

input[type="color"]:hover {
  border-color: var(--border-focus);
}

input[type="color"]::-webkit-color-swatch {
  border-radius: calc(var(--border-radius-md) - 2px);
  border: none;
}

input[type="color"]::-moz-color-swatch {
  border-radius: calc(var(--border-radius-md) - 2px);
  border: none;
}

/* Улучшенные стили для select */
select {
  background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' fill='none' viewBox='0 0 20 20'%3e%3cpath stroke='%236b7280' stroke-linecap='round' stroke-linejoin='round' stroke-width='1.5' d='m6 8 4 4 4-4'/%3e%3c/svg%3e");
  background-position: right 8px center;
  background-repeat: no-repeat;
  background-size: 16px;
  padding-right: 40px;
  cursor: pointer;
}

/* Адаптивность */
@media (max-width: 768px) {
  #app {
    padding: var(--spacing-md);
    gap: var(--spacing-md);
  }

  .btn {
    padding: var(--spacing-md) var(--spacing-lg);
    font-size: 16px;
  }

  input,
  select,
  textarea {
    font-size: 16px;
    /* Предотвращает зум на iOS */
  }
}

/* Улучшения для скроллбара */
::-webkit-scrollbar {
  width: 8px;
}

::-webkit-scrollbar-track {
  background: var(--bg-secondary);
  border-radius: 4px;
}

::-webkit-scrollbar-thumb {
  background: var(--border-color);
  border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
  background: var(--border-hover);
}

/* Улучшения для фокуса */
*:focus-visible {
  outline: 2px solid var(--border-focus);
  outline-offset: 2px;
}

/* Анимации для hover эффектов */
.hover-lift {
  transition: transform var(--transition-normal);
}

.hover-lift:hover {
  transform: translateY(-2px);
}

/* Улучшения для disabled состояний */
.btn:disabled,
input:disabled,
select:disabled,
textarea:disabled {
  opacity: 0.6;
  cursor: not-allowed;
  pointer-events: none;
}

/* Улучшения для loading состояний */
.loading {
  opacity: 0.7;
  pointer-events: none;
}

.loading::after {
  content: '';
  position: absolute;
  top: 50%;
  left: 50%;
  width: 20px;
  height: 20px;
  margin: -10px 0 0 -10px;
  border: 2px solid transparent;
  border-top: 2px solid var(--primary-color);
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% {
    transform: rotate(0deg);
  }

  100% {
    transform: rotate(360deg);
  }
}

/* Стили для виджетов */
.widget {
  background: var(--bg-secondary);
  border: 1px solid var(--border-color);
  border-radius: var(--border-radius-lg);
  padding: var(--spacing-lg);
  box-shadow: 0 4px 6px var(--shadow-color);
  transition: all var(--transition-normal);
  position: relative;
  overflow: hidden;
}

.widget:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px var(--shadow-elevated);
  border-color: var(--border-hover);
}

.widget::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 3px;
  background: linear-gradient(90deg, var(--primary-color), var(--secondary-color));
}

/* Стили для карточек */
.card {
  background: var(--bg-secondary);
  border: 1px solid var(--border-color);
  border-radius: var(--border-radius-md);
  padding: var(--spacing-md);
  transition: all var(--transition-normal);
}

.card:hover {
  background: var(--bg-tertiary);
  border-color: var(--border-hover);
  transform: translateY(-1px);
  box-shadow: 0 4px 12px var(--shadow-color);
}

/* Стили для модальных окон */
.modal {
  background: var(--bg-primary);
  border: 1px solid var(--border-color);
  border-radius: var(--border-radius-lg);
  box-shadow: 0 20px 25px var(--shadow-elevated);
}

.modal-header {
  border-bottom: 1px solid var(--border-color);
  padding: var(--spacing-lg);
}

.modal-body {
  padding: var(--spacing-lg);
}

.modal-footer {
  border-top: 1px solid var(--border-color);
  padding: var(--spacing-lg);
}

/* Стили для вкладок */
.tabs {
  border-bottom: 1px solid var(--border-color);
}

.tab {
  padding: var(--spacing-md) var(--spacing-lg);
  border-bottom: 2px solid transparent;
  color: var(--text-secondary);
  cursor: pointer;
  transition: all var(--transition-normal);
}

.tab.active {
  color: var(--text-primary);
  border-bottom-color: var(--primary-color);
}

.tab:hover {
  color: var(--text-primary);
  background: var(--bg-secondary);
}

/* Стили для групп настроек */
.setting-group {
  background: var(--bg-secondary);
  border: 1px solid var(--border-color);
  border-radius: var(--border-radius-md);
  padding: var(--spacing-lg);
  margin-bottom: var(--spacing-lg);
}

.setting-group h3 {
  margin: 0 0 var(--spacing-md) 0;
  color: var(--text-primary);
  font-size: 1.1em;
  font-weight: 600;
  border-bottom: 1px solid var(--border-color);
  padding-bottom: var(--spacing-sm);
}

.setting-item {
  display: flex;
  align-items: center;
  gap: var(--spacing-md);
  margin-bottom: var(--spacing-md);
  padding: var(--spacing-sm) 0;
}

.setting-item label {
  display: flex;
  align-items: center;
  gap: var(--spacing-sm);
  cursor: pointer;
  flex: 1;
}

.setting-item span {
  color: var(--text-primary);
  font-weight: 500;
}

/* Стили для иконок */
.icon {
  width: 1em;
  height: 1em;
  stroke: currentColor;
  stroke-width: 2;
  fill: none;
  stroke-linecap: round;
  stroke-linejoin: round;
}

.icon-sm {
  width: 16px;
  height: 16px;
}

.icon-md {
  width: 20px;
  height: 20px;
}

.icon-lg {
  width: 24px;
  height: 24px;
}

.icon-xl {
  width: 32px;
  height: 32px;
}

/* Стили для анимаций */
.fade-enter-active,
.fade-leave-active {
  transition: opacity var(--transition-normal);
}

.fade-enter-from,
.fade-leave-to {
  opacity: 0;
}

.slide-enter-active,
.slide-leave-active {
  transition: transform var(--transition-normal);
}

.slide-enter-from {
  transform: translateX(-100%);
}

.slide-leave-to {
  transform: translateX(100%);
}

/* Стили для утилит */
.text-center {
  text-align: center;
}

.text-left {
  text-align: left;
}

.text-right {
  text-align: right;
}

.flex {
  display: flex;
}

.flex-col {
  flex-direction: column;
}

.flex-row {
  flex-direction: row;
}

.items-center {
  align-items: center;
}

.justify-center {
  justify-content: center;
}

.justify-between {
  justify-content: space-between;
}

.gap-xs {
  gap: var(--spacing-xs);
}

.gap-sm {
  gap: var(--spacing-sm);
}

.gap-md {
  gap: var(--spacing-md);
}

.gap-lg {
  gap: var(--spacing-lg);
}

.gap-xl {
  gap: var(--spacing-xl);
}

.mt-xs {
  margin-top: var(--spacing-xs);
}

.mt-sm {
  margin-top: var(--spacing-sm);
}

.mt-md {
  margin-top: var(--spacing-md);
}

.mt-lg {
  margin-top: var(--spacing-lg);
}

.mt-xl {
  margin-top: var(--spacing-xl);
}

.mb-xs {
  margin-bottom: var(--spacing-xs);
}

.mb-sm {
  margin-bottom: var(--spacing-sm);
}

.mb-md {
  margin-bottom: var(--spacing-md);
}

.mb-lg {
  margin-bottom: var(--spacing-lg);
}

.mb-xl {
  margin-bottom: var(--spacing-xl);
}

.p-xs {
  padding: var(--spacing-xs);
}

.p-sm {
  padding: var(--spacing-sm);
}

.p-md {
  padding: var(--spacing-md);
}

.p-lg {
  padding: var(--spacing-lg);
}

.p-xl {
  padding: var(--spacing-xl);
}

.rounded-sm {
  border-radius: var(--border-radius-sm);
}

.rounded-md {
  border-radius: var(--border-radius-md);
}

.rounded-lg {
  border-radius: var(--border-radius-lg);
}

.rounded-xl {
  border-radius: var(--border-radius-xl);
}

.shadow {
  box-shadow: 0 1px 3px var(--shadow-color);
}

.shadow-md {
  box-shadow: 0 4px 6px var(--shadow-color);
}

.shadow-lg {
  box-shadow: 0 10px 15px var(--shadow-color);
}

.shadow-xl {
  box-shadow: 0 20px 25px var(--shadow-color);
}