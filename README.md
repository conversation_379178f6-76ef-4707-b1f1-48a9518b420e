# Veyra - Минималистичная стартовая страница

Veyra — это современное браузерное расширение для создания персонализированной стартовой страницы, вдохновлённое Tabliss. Построено на Vue 3 + WXT с модульной архитектурой плагинов.

## ✨ Особенности

### 🎨 Новый брендинг

- **Название**: Veyra
- **Палитра**: Сиренево-фиолетовые тона с поддержкой кастомизации через color-mix()
- **Логотип**: TikTok Sans
- **Интерфейс**: Onest шрифт
- **UI-шрифт**: Загружается через Google Fonts или системный

### 🖼 Дизайн

- **Главная страница**: Фон на весь экран, всегда позади элементов
- **Кнопка настроек**: В углу → открывает sidebar слева
- **Виджеты**: Прозрачный фон, ничего отвлекающего
  - Часы — простое время
  - Ссылки — grid с favicon'ами или названиями (по выбору)

### ⚙ Настройки

#### Фон

- 5 готовых цветов + input color
- Градиенты: линейный, радиальный, конический
- Кастомизация (несколько радиальных, drag'n'drop, цвет/прозрачность)
- Unsplash API (и аналоги)
- Анимации: линейные/радиальные/конические вращаются, кастомные радиальные перемещаются/меняют прозрачность
- Поддержка Vue Bits (фоны)

#### Ссылки

- Форматы: иконка+текст / иконка / текст
- Источники: Favicon, Simple Icons, Iconify, Custom SVG
- Папки: вложенные иконки (popover), раскрытие/скрытие по клику
- Drag'n'drop сортировка
- Массовое редактирование (смешение Favicon + Iconify)

#### Время

- Отображение: часы+минуты / часы / часы+минуты+секунды
- Кастомизация каждого разряда (большие часы, маленькие минуты сверху и т. д.)
- Альтернативный режим: «Эй, двенадцать часов, пора бы за работу»

#### Импорт / Экспорт

- Импорт .veyra.json и tabliss.json (включая форки tablissng.json)
- Частичный импорт (выборочные данные)
- Экспорт .veyra.json
- Публикация пресетов в Tabtail

#### Внешний вид

- Шрифты: Google Fonts или системные
- Светлая/тёмная тема
- Акцентный цвет

## 🛠 Технические требования

### Стек

- **WXT** + **Vue 3** + **JavaScript** (TS только если без него невозможно)
- **Библиотеки**: Lucide Icons, Iconify, GSAP, Vue Bits
- **Система плагинов**: простой синтаксис
- **Архитектура**: устойчивая к ошибкам с использованием try/catch

### Особенности

- 🚫 **Не использует Tailwind CSS**
- ✅ **Manifest v3**
- 💾 **Настройки хранятся в chrome.storage.sync**
- 📱 **UI адаптивный** с плавными анимациями

## 📁 Структура проекта

```
/public
  manifest.json
  icons/
/entrypoints
  /newtab
    /components     # Vue компоненты
    /widgets        # Компоненты виджетов
    /plugins        # Плагины системы
    /utils          # Утилиты и хелперы
    /styles         # Стили приложения
    App.vue         # Главный компонент
    main.js         # Точка входа
```

## 🚀 Быстрый старт

### Установка зависимостей

```bash
npm install
```

### Разработка

```bash
npm run dev
```

### Сборка для продакшена

```bash
npm run build
```

### Сборка для Firefox

```bash
npm run build:firefox
```

### Создание ZIP архива

```bash
npm run zip
```

## 🔌 Система плагинов

Veyra использует модульную архитектуру плагинов. Каждый плагин представляет собой независимый модуль с собственными настройками и компонентами.

### Базовые плагины

- **Фон** (`background`) - Управление фоном страницы
- **Время** (`time`) - Отображение времени и даты
- **Ссылки** (`links`) - Управление ссылками и папками

### Создание плагина

```javascript
import { BasePlugin } from "../utils/BasePlugin.js";
import MyComponent from "../widgets/MyWidget.vue";

class MyPluginClass extends BasePlugin {
	constructor() {
		super({
			id: "my-plugin",
			name: "Мой плагин",
			version: "1.0.0",
			description: "Описание плагина",
			component: MyComponent,
			defaultSettings: {
				// настройки по умолчанию
			},
		});
	}

	async onInit() {
		// инициализация плагина
	}

	async onDestroy() {
		// очистка плагина
	}
}

export const MyPlugin = new MyPluginClass();
```

## 🎨 Кастомизация

### Темы

Veyra поддерживает светлую и тёмную темы, а также автоматическое переключение в зависимости от системных настроек.

### Цвета

Акцентный цвет можно настроить в разделе "Внешний вид" панели настроек.

### Шрифты

Поддерживаются Google Fonts и системные шрифты:

- Onest (рекомендуется)
- Inter
- Roboto
- Системный шрифт

## 📦 Импорт/Экспорт

### Поддерживаемые форматы

- `.veyra.json` - нативный формат Veyra
- `tabliss.json` - импорт из Tabliss
- `tablissng.json` - импорт из форков Tabliss

### Экспорт настроек

```javascript
import { exportVeyraSettings } from "./utils/importExport.js";

const settings = await exportVeyraSettings();
```

### Импорт настроек

```javascript
import { importVeyraSettings } from "./utils/importExport.js";

await importVeyraSettings(data, {
	importAppearance: true,
	importPlugins: true,
	overwriteExisting: true,
});
```

## 🤝 Вклад в проект

1. Форкните репозиторий
2. Создайте ветку для новой функции (`git checkout -b feature/amazing-feature`)
3. Зафиксируйте изменения (`git commit -m 'Add amazing feature'`)
4. Отправьте в ветку (`git push origin feature/amazing-feature`)
5. Откройте Pull Request

## 📄 Лицензия

Этот проект лицензирован под MIT License - см. файл [LICENSE](LICENSE) для деталей.

## 🙏 Благодарности

- [Tabliss](https://tabliss.io/) - за вдохновение
- [WXT](https://wxt.dev/) - за отличный фреймворк для расширений
- [Vue.js](https://vuejs.org/) - за реактивность
- [Lucide](https://lucide.dev/) - за красивые иконки
