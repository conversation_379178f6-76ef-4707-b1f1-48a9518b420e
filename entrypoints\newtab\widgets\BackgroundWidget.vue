<template>
  <div 
    class="background-widget"
    :style="backgroundStyle"
    :class="{ 
      'animated': enableAnimation,
      [`animation-${animationType}`]: enableAnimation 
    }"
  >
    <!-- Overlay слой если включен -->
    <div 
      v-if="overlay" 
      class="background-overlay"
      :style="{ backgroundColor: overlayColor }"
    ></div>
  </div>
</template>

<script setup>
import { computed, ref, onMounted, onUnmounted, watch } from 'vue';
import { pluginSystem } from '../utils/pluginSystem.js';

const props = defineProps({
  pluginId: {
    type: String,
    required: true
  }
});

// Получаем плагин
const plugin = computed(() => pluginSystem.getPlugin(props.pluginId));
const settings = ref({});

// Реактивные настройки
const backgroundType = computed(() => settings.value.type || 'gradient');
const enableAnimation = computed(() => settings.value.enableAnimation || false);
const animationType = computed(() => settings.value.animationType || 'rotate');
const overlay = computed(() => settings.value.overlay || false);
const overlayColor = computed(() => settings.value.overlayColor || 'rgba(0, 0, 0, 0.3)');

// Генерируем стиль фона
const backgroundStyle = computed(() => {
  if (!plugin.value) return {};
  
  const css = plugin.value.generateBackgroundCSS();
  const styles = {};
  
  // Парсим CSS строку в объект стилей
  css.split(';').forEach(rule => {
    const [property, value] = rule.split(':').map(s => s.trim());
    if (property && value) {
      // Конвертируем CSS свойства в camelCase
      const camelProperty = property.replace(/-([a-z])/g, (g) => g[1].toUpperCase());
      styles[camelProperty] = value;
    }
  });
  
  return styles;
});

// Обновляем настройки при изменении плагина
const updateSettings = () => {
  if (plugin.value) {
    settings.value = plugin.value.getSettings();
  }
};

// Слушатели событий плагина
const onSettingsUpdated = () => {
  updateSettings();
};

const onBackgroundChanged = () => {
  updateSettings();
};

const onImageLoaded = (event) => {
  updateSettings();
};

onMounted(() => {
  if (plugin.value) {
    updateSettings();
    
    // Подписываемся на события плагина
    plugin.value.addEventListener('settings-updated', onSettingsUpdated);
    plugin.value.addEventListener('background-changed', onBackgroundChanged);
    plugin.value.addEventListener('image-loaded', onImageLoaded);
  }
});

onUnmounted(() => {
  if (plugin.value) {
    // Отписываемся от событий
    plugin.value.removeEventListener('settings-updated', onSettingsUpdated);
    plugin.value.removeEventListener('background-changed', onBackgroundChanged);
    plugin.value.removeEventListener('image-loaded', onImageLoaded);
  }
});

// Следим за изменениями плагина
watch(() => plugin.value, (newPlugin) => {
  if (newPlugin) {
    updateSettings();
  }
});
</script>

<style scoped>
.background-widget {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: -1;
  transition: all 0.3s ease;
}

.background-overlay {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  pointer-events: none;
}

/* Анимации */
.animated.animation-rotate {
  animation: backgroundRotate var(--animation-duration, 5s) linear infinite;
}

.animated.animation-pulse {
  animation: backgroundPulse var(--animation-duration, 5s) ease-in-out infinite alternate;
}

.animated.animation-wave {
  animation: backgroundWave var(--animation-duration, 5s) ease-in-out infinite;
}

@keyframes backgroundRotate {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

@keyframes backgroundPulse {
  from {
    transform: scale(1);
    opacity: 1;
  }
  to {
    transform: scale(1.05);
    opacity: 0.8;
  }
}

@keyframes backgroundWave {
  0%, 100% {
    transform: translateY(0) scale(1);
  }
  25% {
    transform: translateY(-10px) scale(1.02);
  }
  50% {
    transform: translateY(0) scale(1);
  }
  75% {
    transform: translateY(10px) scale(0.98);
  }
}

/* Адаптивность */
@media (max-width: 768px) {
  .background-widget {
    /* Отключаем сложные анимации на мобильных */
  }
  
  .animated.animation-rotate,
  .animated.animation-wave {
    animation: none;
  }
}

/* Поддержка prefers-reduced-motion */
@media (prefers-reduced-motion: reduce) {
  .animated {
    animation: none !important;
  }
  
  .background-widget {
    transition: none;
  }
}
</style>
