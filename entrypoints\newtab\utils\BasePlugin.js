/**
 * Базовый класс для плагинов Veyra
 * Предоставляет общую функциональность для всех плагинов
 */

export class BasePlugin {
  constructor(config) {
    this.id = config.id;
    this.name = config.name;
    this.version = config.version || '1.0.0';
    this.description = config.description || '';
    this.defaultSettings = config.defaultSettings || {};
    this.component = config.component;
    this.settingsComponent = config.settingsComponent;
    
    // Внутреннее состояние
    this._initialized = false;
    this._settings = {};
    this._eventListeners = new Map();
  }

  /**
   * Инициализация плагина
   * Переопределяется в дочерних классах
   */
  async init() {
    if (this._initialized) {
      console.warn(`Plugin ${this.id} is already initialized`);
      return;
    }

    try {
      // Загружаем настройки
      await this.loadSettings();
      
      // Выполняем пользовательскую инициализацию
      await this.onInit();
      
      this._initialized = true;
      console.log(`Plugin ${this.id} initialized successfully`);
    } catch (error) {
      console.error(`Failed to initialize plugin ${this.id}:`, error);
      throw error;
    }
  }

  /**
   * Очистка плагина
   * Переопределяется в дочерних классах
   */
  async destroy() {
    if (!this._initialized) {
      console.warn(`Plugin ${this.id} is not initialized`);
      return;
    }

    try {
      // Выполняем пользовательскую очистку
      await this.onDestroy();
      
      // Очищаем слушатели событий
      this.removeAllEventListeners();
      
      this._initialized = false;
      console.log(`Plugin ${this.id} destroyed successfully`);
    } catch (error) {
      console.error(`Failed to destroy plugin ${this.id}:`, error);
      throw error;
    }
  }

  /**
   * Пользовательская инициализация
   * Переопределяется в дочерних классах
   */
  async onInit() {
    // Переопределить в дочернем классе
  }

  /**
   * Пользовательская очистка
   * Переопределяется в дочерних классах
   */
  async onDestroy() {
    // Переопределить в дочернем классе
  }

  /**
   * Загружает настройки плагина
   */
  async loadSettings() {
    try {
      const result = await chrome.storage.sync.get(`plugin_${this.id}`);
      this._settings = { ...this.defaultSettings, ...result[`plugin_${this.id}`] };
      return this._settings;
    } catch (error) {
      console.error(`Failed to load settings for plugin ${this.id}:`, error);
      this._settings = { ...this.defaultSettings };
      return this._settings;
    }
  }

  /**
   * Сохраняет настройки плагина
   */
  async saveSettings(settings = null) {
    try {
      const settingsToSave = settings || this._settings;
      await chrome.storage.sync.set({ [`plugin_${this.id}`]: settingsToSave });
      if (settings) {
        this._settings = { ...this._settings, ...settings };
      }
      return true;
    } catch (error) {
      console.error(`Failed to save settings for plugin ${this.id}:`, error);
      return false;
    }
  }

  /**
   * Получает настройку по ключу
   */
  getSetting(key, defaultValue = null) {
    return this._settings[key] !== undefined ? this._settings[key] : defaultValue;
  }

  /**
   * Устанавливает настройку
   */
  async setSetting(key, value) {
    this._settings[key] = value;
    await this.saveSettings();
    this.emit('setting-changed', { key, value });
  }

  /**
   * Получает все настройки
   */
  getSettings() {
    return { ...this._settings };
  }

  /**
   * Обновляет несколько настроек
   */
  async updateSettings(settings) {
    const oldSettings = { ...this._settings };
    this._settings = { ...this._settings, ...settings };
    await this.saveSettings();
    this.emit('settings-updated', { oldSettings, newSettings: this._settings });
  }

  /**
   * Сбрасывает настройки к значениям по умолчанию
   */
  async resetSettings() {
    this._settings = { ...this.defaultSettings };
    await this.saveSettings();
    this.emit('settings-reset');
  }

  /**
   * Добавляет слушатель события
   */
  addEventListener(event, callback) {
    if (!this._eventListeners.has(event)) {
      this._eventListeners.set(event, new Set());
    }
    this._eventListeners.get(event).add(callback);
  }

  /**
   * Удаляет слушатель события
   */
  removeEventListener(event, callback) {
    if (this._eventListeners.has(event)) {
      this._eventListeners.get(event).delete(callback);
    }
  }

  /**
   * Удаляет все слушатели событий
   */
  removeAllEventListeners() {
    this._eventListeners.clear();
  }

  /**
   * Отправляет событие
   */
  emit(event, data = null) {
    if (this._eventListeners.has(event)) {
      this._eventListeners.get(event).forEach(callback => {
        try {
          callback(data);
        } catch (error) {
          console.error(`Error in event listener for ${event}:`, error);
        }
      });
    }
  }

  /**
   * Проверяет, инициализирован ли плагин
   */
  isInitialized() {
    return this._initialized;
  }

  /**
   * Получает информацию о плагине
   */
  getInfo() {
    return {
      id: this.id,
      name: this.name,
      version: this.version,
      description: this.description,
      initialized: this._initialized
    };
  }

  /**
   * Валидирует настройки
   * Переопределяется в дочерних классах
   */
  validateSettings(settings) {
    return true;
  }

  /**
   * Экспортирует настройки плагина
   */
  exportSettings() {
    return {
      pluginId: this.id,
      version: this.version,
      settings: this.getSettings()
    };
  }

  /**
   * Импортирует настройки плагина
   */
  async importSettings(data) {
    try {
      if (data.pluginId !== this.id) {
        throw new Error(`Settings are for plugin ${data.pluginId}, not ${this.id}`);
      }

      if (!this.validateSettings(data.settings)) {
        throw new Error('Invalid settings data');
      }

      await this.updateSettings(data.settings);
      return true;
    } catch (error) {
      console.error(`Failed to import settings for plugin ${this.id}:`, error);
      return false;
    }
  }
}

export default BasePlugin;
