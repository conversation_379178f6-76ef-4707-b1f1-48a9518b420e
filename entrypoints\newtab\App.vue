<template>
	<div class="veyra-app" :class="{ 'settings-open': showSettings }">
		<!-- Фон на весь экран -->
		<div class="background-layer">
			<component
				v-if="backgroundPlugin"
				:is="backgroundPlugin.component"
				:plugin-id="backgroundPlugin.id"
			/>
		</div>

		<!-- Основной контент -->
		<div class="main-content">
			<!-- Виджеты плагинов -->
			<component
				v-for="plugin in activeWidgetPlugins"
				:key="plugin.id"
				:is="plugin.component"
				:plugin-id="plugin.id"
				class="widget-component"
			/>
		</div>

		<!-- Кнопка настроек -->
		<button
			@click="toggleSettings"
			class="settings-toggle-btn"
			:title="'Настройки'"
		>
			<Settings class="settings-toggle-icon" />
		</button>

		<!-- Панель настроек (sidebar слева) -->
		<transition name="sidebar">
			<div v-if="showSettings" class="settings-sidebar">
				<SettingsPanel @close="showSettings = false" />
			</div>
		</transition>

		<!-- Overlay для закрытия настроек -->
		<div
			v-if="showSettings"
			class="settings-overlay"
			@click="showSettings = false"
		></div>
	</div>
</template>

<script setup>
import { computed, ref, onMounted } from "vue";
import { Settings } from "lucide-vue-next";
import SettingsPanel from "./components/VeyraSettingsPanel.vue";
import { pluginSystem } from "./utils/pluginSystem.js";

// Состояние приложения
const showSettings = ref(false);

// Активные плагины
const backgroundPlugin = computed(() => {
	const plugins = pluginSystem.getActivePlugins();
	return plugins.find((plugin) => plugin.id === "background");
});

const activeWidgetPlugins = computed(() => {
	const plugins = pluginSystem.getActivePlugins();
	return plugins.filter((plugin) => plugin.id !== "background");
});

// Методы
const toggleSettings = () => {
	showSettings.value = !showSettings.value;
};

// Инициализация при монтировании
onMounted(async () => {
	try {
		// Регистрируем базовые плагины
		await registerCorePlugins();

		// Инициализируем систему плагинов
		await pluginSystem.init();

		console.log("Veyra app initialized successfully");
	} catch (error) {
		console.error("Failed to initialize Veyra app:", error);
	}
});

// Регистрация основных плагинов
async function registerCorePlugins() {
	// Импортируем плагины динамически
	const { BackgroundPlugin } = await import("./plugins/BackgroundPlugin.js");
	const { TimePlugin } = await import("./plugins/TimePlugin.js");
	const { LinksPlugin } = await import("./plugins/LinksPlugin.js");

	// Регистрируем плагины
	pluginSystem.registerPlugin(BackgroundPlugin);
	pluginSystem.registerPlugin(TimePlugin);
	pluginSystem.registerPlugin(LinksPlugin);
}
</script>

<style scoped>
.veyra-app {
	position: relative;
	width: 100vw;
	height: 100vh;
	overflow: hidden;
	font-family: "Onest", -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto,
		sans-serif;
}

.background-layer {
	position: absolute;
	top: 0;
	left: 0;
	width: 100%;
	height: 100%;
	z-index: 0;
}

.main-content {
	position: relative;
	z-index: 1;
	width: 100%;
	height: 100%;
	display: flex;
	flex-direction: column;
	align-items: center;
	justify-content: center;
	padding: 20px;
	box-sizing: border-box;
}

.widget-component {
	margin: 10px;
	background: transparent;
}

.settings-toggle-btn {
	position: fixed;
	top: 20px;
	right: 20px;
	z-index: 1001;
	background: rgba(139, 69, 199, 0.2);
	border: 1px solid rgba(139, 69, 199, 0.3);
	border-radius: 50%;
	width: 50px;
	height: 50px;
	display: flex;
	align-items: center;
	justify-content: center;
	cursor: pointer;
	transition: all 0.3s ease;
	backdrop-filter: blur(10px);
}

.settings-toggle-btn:hover {
	background: rgba(139, 69, 199, 0.3);
	transform: scale(1.1);
	box-shadow: 0 4px 20px rgba(139, 69, 199, 0.3);
}

.settings-toggle-icon {
	width: 24px;
	height: 24px;
	color: white;
}

.settings-sidebar {
	position: fixed;
	top: 0;
	left: 0;
	width: 400px;
	height: 100vh;
	background: rgba(255, 255, 255, 0.95);
	backdrop-filter: blur(20px);
	z-index: 1002;
	box-shadow: 2px 0 20px rgba(0, 0, 0, 0.1);
}

.settings-overlay {
	position: fixed;
	top: 0;
	left: 0;
	width: 100vw;
	height: 100vh;
	background: rgba(0, 0, 0, 0.3);
	z-index: 1001;
	backdrop-filter: blur(2px);
}

.sidebar-enter-active,
.sidebar-leave-active {
	transition: transform 0.3s ease;
}

.sidebar-enter-from,
.sidebar-leave-to {
	transform: translateX(-100%);
}

/* Темная тема */
@media (prefers-color-scheme: dark) {
	.settings-sidebar {
		background: rgba(30, 30, 30, 0.95);
		color: white;
	}
}
</style>
