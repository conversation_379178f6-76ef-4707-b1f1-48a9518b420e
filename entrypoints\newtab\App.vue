<template>
	<Background />

	<!-- Кнопка настроек -->
	<button
		@click="toggleSettings"
		class="settings-toggle-btn"
		:title="$t('common.settings')"
	>
		<Settings class="settings-toggle-icon" />
	</button>

	<!-- Панель настроек (скрывается/показывается) -->
	<SettingsPanel v-if="showSettings" @close="showSettings = false" />

	<!-- Динамические виджеты без заголовков -->
	<component
		v-for="widget in activeWidgets"
		:key="widget.id"
		:is="widget.component"
		:widget-id="widget.id"
		class="widget-component"
	/>
</template>

<script setup>
import { computed, ref } from "vue";
import { useSettingsStore } from "./stores/settingsStore.js";
import Background from "./components/Background.vue";
import SettingsPanel from "./components/SettingsPanel.vue";
import { Settings } from "lucide-vue-next";

// Импорт всех виджетов
import TimeDisplay from "./components/TimeDisplay.vue";
import LinksDisplay from "./components/LinksDisplay.vue";
import WeatherWidget from "./components/WeatherWidget.vue";
import NotesWidget from "./components/NotesWidget.vue";

const settingsStore = useSettingsStore();
const showSettings = ref(false);

// Динамически активные виджеты
const activeWidgets = computed(() => {
	return settingsStore.activeWidgetsList
		.map((w) => {
			// Сопоставление имени компонента с импортированным
			switch (w.component) {
				case "TimeDisplay":
					return { ...w, component: TimeDisplay };
				case "LinksDisplay":
					return { ...w, component: LinksDisplay };
				case "WeatherWidget":
					return { ...w, component: WeatherWidget };
				case "NotesWidget":
					return { ...w, component: NotesWidget };
				default:
					return null;
			}
		})
		.filter(Boolean); // Фильтрация нераспознанных
});

// Методы
const toggleSettings = () => {
	showSettings.value = !showSettings.value;
};
</script>

<style scoped>
.settings-toggle-btn {
	position: fixed;
	top: 20px;
	right: 20px;
	z-index: 1000;
	background: rgba(255, 255, 255, 0.1);
	border: 1px solid rgba(255, 255, 255, 0.2);
	border-radius: 50%;
	width: 50px;
	height: 50px;
	display: flex;
	align-items: center;
	justify-content: center;
	cursor: pointer;
	transition: all 0.3s ease;
	backdrop-filter: blur(10px);
}

.settings-toggle-btn:hover {
	background: rgba(255, 255, 255, 0.2);
	transform: scale(1.1);
}

.settings-toggle-icon {
	width: 24px;
	height: 24px;
	color: white;
}

.widget-component {
	/* Убираем лишние отступы и заголовки у виджетов */
	margin: 10px;
}
</style>
