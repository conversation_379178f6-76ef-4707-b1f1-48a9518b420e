import { defineConfig } from "wxt";

export default defineConfig({
	modules: ["@wxt-dev/module-vue"],
	framework: "vue",
	type: "newtab",
	manifest: {
		manifest_version: 3,
		name: "<PERSON><PERSON><PERSON>",
		description: "Персонализируемая стартовая страница с виджетами",
		version: "1.0.0",
		icons: { 48: "icon/48.png", 128: "icon/128.png" },
		chrome_url_overrides: {
			newtab: "newtab/index.html",
		},
		permissions: ["storage"],
	},
});
