import { defineConfig } from "wxt";

export default defineConfig({
	modules: ["@wxt-dev/module-vue"],
	framework: "vue",
	manifest: {
		manifest_version: 3,
		name: "<PERSON><PERSON><PERSON>",
		description: "Минималистичная стартовая страница, вдохновлённая Tabliss",
		version: "1.0.0",
		icons: {
			16: "icon/16.png",
			32: "icon/32.png",
			48: "icon/48.png",
			128: "icon/128.png",
		},
		chrome_url_overrides: {
			newtab: "newtab/index.html",
		},
		permissions: ["storage", "activeTab"],
		host_permissions: ["https://api.unsplash.com/*"],
		content_security_policy: {
			extension_pages:
				"script-src 'self'; object-src 'self'; font-src 'self' https://fonts.gstatic.com; style-src 'self' 'unsafe-inline' https://fonts.googleapis.com;",
		},
	},
});
